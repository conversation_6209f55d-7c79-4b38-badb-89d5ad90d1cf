import { <PERSON><PERSON><PERSON>eader } from './components/display/chat-header.js';
import { InstructionsDisplay } from './components/display/instructions-display.js';
import { ResponseFormatter } from './components/display/response-formatter.js';
import { MessageInput } from './components/input/message-input.js';
import { MessageManager } from './components/conversation/message-manager.js';
import { ConversationState } from './components/conversation/conversation-state.js';
import { CommandHandler } from './components/commands/command-handler.js';
import { ProviderInterface } from './components/providers/provider-interface.js';
import { ModelSelector } from './components/model-selector.js';
import UIHelpers from './utils/ui-helpers.js';
import configManager from '../../config/config-manager.js';
import ProviderFactory from '../../providers/index.js';
import logger from '../../utils/logger.js';
import chalk from 'chalk';

/**
 * Main chat interface orchestrator
 */
export class ChatInterface {
  constructor() {
    this.messageManager = new MessageManager();
    this.conversationState = new ConversationState();
    this.commandHandler = new CommandHandler();
    this.providerInterface = null;
    this.isRunning = false;
    
    this.setupCommandHandler();
    this.setupEventHandlers();
  }

  /**
   * Start the chat interface
   */
  async start() {
    try {
      this.isRunning = true;
      
      // Initialize provider
      await this.initializeProvider();
      
      // Display initial interface
      this.displayWelcome();
      
      // Start main chat loop
      await this.chatLoop();
      
    } catch (error) {
      logger.error('Chat interface error:', error.message);
      console.log(chalk.red(`Error: ${error.message}`));
      this.stop();
    }
  }

  /**
   * Stop the chat interface
   */
  stop() {
    this.isRunning = false;
    this.conversationState.end();
    logger.info('Chat interface stopped');
  }

  /**
   * Initialize the provider interface
   */
  async initializeProvider() {
    const activeProvider = configManager.getActiveProvider();
    if (!activeProvider) {
      throw new Error('No active provider configured');
    }

    const provider = ProviderFactory.createProvider(activeProvider.name);
    this.providerInterface = new ProviderInterface(provider);
    
    this.conversationState.initialize(activeProvider.name, activeProvider.config.defaultModel);
    
    logger.info('Provider initialized', { 
      provider: activeProvider.name, 
      model: activeProvider.config.defaultModel 
    });
  }

  /**
   * Display welcome screen
   */
  displayWelcome() {
    const state = this.conversationState.getCurrentState();

    ChatHeader.display(state.currentProvider, state.currentModel);
    InstructionsDisplay.displayQuickStart();
  }

  /**
   * Main chat loop
   */
  async chatLoop() {
    while (this.isRunning) {
      try {
        // Get user input
        const input = await MessageInput.getMessage({
          prompt: UIHelpers.colors.primary('▶'),
          placeholder: 'Type your message or /help for commands...'
        });

        if (!input) continue;

        // Check if it's a command
        if (input.startsWith('/')) {
          const result = await this.commandHandler.execute(input, this);
          if (result === 'exit') {
            break;
          }
          continue;
        }

        // Process regular message
        await this.processMessage(input);

      } catch (error) {
        if (error.message.includes('User force closed')) {
          break;
        }
        
        logger.error('Chat loop error:', error.message);
        console.log(chalk.red(`Error: ${error.message}`));
      }
    }
    
    this.stop();
  }

  /**
   * Process a regular chat message
   */
  async processMessage(message) {
    try {
      this.conversationState.startProcessing();

      // Add user message
      this.messageManager.addUserMessage(message);

      // Show processing indicator
      const spinner = UIHelpers.createSpinner('Thinking...');
      spinner.start();

      // Get conversation history
      const messages = this.messageManager.getMessagesForApi();

      // Send to provider
      const state = this.conversationState.getCurrentState();
      const response = await this.providerInterface.sendMessage(messages, {
        model: state.currentModel,
        systemPrompt: configManager.get('chat.systemPrompt')
      });

      spinner.stop();

      // Add assistant response
      this.messageManager.addAssistantMessage(response.content, {
        usage: response.usage,
        model: state.currentModel
      });

      // Display response
      ResponseFormatter.display(response.content, {
        showMetadata: true,
        usage: response.usage
      });

    } catch (error) {
      this.conversationState.stopProcessing();
      throw error;
    } finally {
      this.conversationState.stopProcessing();
    }
  }

  /**
   * Setup command handler with context
   */
  setupCommandHandler() {
    this.commandHandler.setContext(this);

    // Register basic commands
    this.commandHandler.registry.register('help', {
      description: 'Show available commands',
      execute: () => {
        InstructionsDisplay.display();
      }
    });

    this.commandHandler.registry.register('clear', {
      description: 'Clear conversation history',
      execute: () => {
        this.messageManager.clearMessages();
        UIHelpers.clearScreen();
        this.displayWelcome();
      }
    });

    this.commandHandler.registry.register('model', {
      description: 'Change current model',
      execute: async () => {
        const newModel = await ModelSelector.selectModel();
        if (newModel) {
          this.conversationState.updateModel(newModel);
          console.log(chalk.green(`Model changed to: ${newModel}`));
        }
      }
    });

    this.commandHandler.registry.register('history', {
      description: 'Show conversation history',
      execute: () => {
        const messages = this.messageManager.getAllMessages();
        if (messages.length === 0) {
          console.log(chalk.yellow('No conversation history'));
          return;
        }

        messages.forEach((msg, index) => {
          const role = msg.role === 'user' ? 'You' : 'Assistant';
          const color = msg.role === 'user' ? chalk.blue : chalk.green;
          console.log(color(`${index + 1}. ${role}: ${msg.content.substring(0, 100)}...`));
        });
      }
    });

    this.commandHandler.registry.register('exit', {
      description: 'Exit the chat interface',
      execute: () => {
        console.log(chalk.yellow('Goodbye!'));
        return 'exit';
      }
    });

    this.commandHandler.registry.register('quit', {
      description: 'Exit the chat interface',
      aliases: ['q'],
      execute: () => {
        console.log(chalk.yellow('Goodbye!'));
        return 'exit';
      }
    });
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    this.conversationState.on('providerChanged', (provider) => {
      logger.info('Provider changed', { provider });
    });

    this.conversationState.on('modelChanged', (model) => {
      logger.info('Model changed', { model });
    });
  }

  /**
   * Get current conversation state
   */
  getState() {
    return this.conversationState.getCurrentState();
  }

  /**
   * Get message manager
   */
  getMessageManager() {
    return this.messageManager;
  }

  /**
   * Get provider interface
   */
  getProviderInterface() {
    return this.providerInterface;
  }
}
