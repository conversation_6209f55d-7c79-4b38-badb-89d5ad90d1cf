import logger from '../../../../utils/logger.js';

/**
 * Command registry for managing available chat commands
 */
export class CommandRegistry {
  constructor() {
    this.commands = new Map();
    this.aliases = new Map();
  }

  /**
   * Register a command
   * @param {string} name - Command name
   * @param {object} command - Command object
   */
  register(name, command) {
    if (!name || typeof name !== 'string') {
      throw new Error('Command name must be a non-empty string');
    }

    if (!command || typeof command.execute !== 'function') {
      throw new Error('Command must have an execute function');
    }

    // Validate command structure
    this.validateCommand(command);

    this.commands.set(name.toLowerCase(), {
      name: name.toLowerCase(),
      description: command.description || 'No description available',
      usage: command.usage || `/${name}`,
      examples: command.examples || [],
      category: command.category || 'general',
      hidden: command.hidden || false,
      execute: command.execute,
      validate: command.validate || null,
      ...command
    });

    // Register aliases if provided
    if (command.aliases && Array.isArray(command.aliases)) {
      command.aliases.forEach(alias => {
        this.aliases.set(alias.toLowerCase(), name.toLowerCase());
      });
    }

    logger.debug('Command registered', { name, aliases: command.aliases });
  }

  /**
   * Unregister a command
   * @param {string} name - Command name
   * @returns {boolean} True if command was removed
   */
  unregister(name) {
    const commandName = name.toLowerCase();
    const command = this.commands.get(commandName);

    if (!command) {
      return false;
    }

    // Remove aliases
    if (command.aliases) {
      command.aliases.forEach(alias => {
        this.aliases.delete(alias.toLowerCase());
      });
    }

    this.commands.delete(commandName);
    logger.debug('Command unregistered', { name: commandName });
    return true;
  }

  /**
   * Get a command by name or alias
   * @param {string} name - Command name or alias
   * @returns {object|null} Command object or null if not found
   */
  get(name) {
    const commandName = name.toLowerCase();
    
    // Check direct command name
    if (this.commands.has(commandName)) {
      return this.commands.get(commandName);
    }

    // Check aliases
    const aliasTarget = this.aliases.get(commandName);
    if (aliasTarget && this.commands.has(aliasTarget)) {
      return this.commands.get(aliasTarget);
    }

    return null;
  }

  /**
   * Check if a command exists
   * @param {string} name - Command name or alias
   * @returns {boolean} True if command exists
   */
  has(name) {
    return this.get(name) !== null;
  }

  /**
   * Get all registered commands
   * @param {object} options - Filter options
   * @returns {Array} Array of command objects
   */
  getAll(options = {}) {
    const defaultOptions = {
      includeHidden: false,
      category: null,
      ...options
    };

    let commands = Array.from(this.commands.values());

    // Filter hidden commands
    if (!defaultOptions.includeHidden) {
      commands = commands.filter(cmd => !cmd.hidden);
    }

    // Filter by category
    if (defaultOptions.category) {
      commands = commands.filter(cmd => cmd.category === defaultOptions.category);
    }

    return commands.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Get command names
   * @param {object} options - Filter options
   * @returns {Array} Array of command names
   */
  getNames(options = {}) {
    return this.getAll(options).map(cmd => cmd.name);
  }

  /**
   * Get commands by category
   * @returns {object} Commands grouped by category
   */
  getByCategory() {
    const commands = this.getAll();
    const categories = {};

    commands.forEach(cmd => {
      if (!categories[cmd.category]) {
        categories[cmd.category] = [];
      }
      categories[cmd.category].push(cmd);
    });

    return categories;
  }

  /**
   * Search commands by name or description
   * @param {string} query - Search query
   * @param {object} options - Search options
   * @returns {Array} Array of matching commands
   */
  search(query, options = {}) {
    const defaultOptions = {
      caseSensitive: false,
      searchDescription: true,
      searchAliases: true,
      ...options
    };

    const searchTerm = defaultOptions.caseSensitive ? query : query.toLowerCase();
    const commands = this.getAll();

    return commands.filter(cmd => {
      const name = defaultOptions.caseSensitive ? cmd.name : cmd.name.toLowerCase();
      const description = defaultOptions.caseSensitive ? cmd.description : cmd.description.toLowerCase();

      // Search in name
      if (name.includes(searchTerm)) {
        return true;
      }

      // Search in description
      if (defaultOptions.searchDescription && description.includes(searchTerm)) {
        return true;
      }

      // Search in aliases
      if (defaultOptions.searchAliases && cmd.aliases) {
        const aliases = defaultOptions.caseSensitive ? cmd.aliases : cmd.aliases.map(a => a.toLowerCase());
        if (aliases.some(alias => alias.includes(searchTerm))) {
          return true;
        }
      }

      return false;
    });
  }

  /**
   * Get command suggestions for partial input
   * @param {string} partial - Partial command name
   * @param {number} limit - Maximum suggestions to return
   * @returns {Array} Array of suggested command names
   */
  getSuggestions(partial, limit = 5) {
    const partialLower = partial.toLowerCase();
    const commands = this.getAll();
    
    const suggestions = [];

    // Exact prefix matches first
    commands.forEach(cmd => {
      if (cmd.name.startsWith(partialLower)) {
        suggestions.push(cmd.name);
      }
    });

    // Alias prefix matches
    for (const [alias, commandName] of this.aliases) {
      if (alias.startsWith(partialLower) && !suggestions.includes(commandName)) {
        suggestions.push(commandName);
      }
    }

    // Partial matches in name
    commands.forEach(cmd => {
      if (!suggestions.includes(cmd.name) && cmd.name.includes(partialLower)) {
        suggestions.push(cmd.name);
      }
    });

    return suggestions.slice(0, limit);
  }

  /**
   * Validate command structure
   * @param {object} command - Command object to validate
   */
  validateCommand(command) {
    const requiredFields = ['execute'];
    const optionalFields = ['description', 'usage', 'examples', 'category', 'hidden', 'aliases', 'validate'];

    // Check required fields
    requiredFields.forEach(field => {
      if (!(field in command)) {
        throw new Error(`Command missing required field: ${field}`);
      }
    });

    // Validate execute function
    if (typeof command.execute !== 'function') {
      throw new Error('Command execute must be a function');
    }

    // Validate optional fields
    if (command.description && typeof command.description !== 'string') {
      throw new Error('Command description must be a string');
    }

    if (command.usage && typeof command.usage !== 'string') {
      throw new Error('Command usage must be a string');
    }

    if (command.examples && !Array.isArray(command.examples)) {
      throw new Error('Command examples must be an array');
    }

    if (command.aliases && !Array.isArray(command.aliases)) {
      throw new Error('Command aliases must be an array');
    }

    if (command.validate && typeof command.validate !== 'function') {
      throw new Error('Command validate must be a function');
    }
  }

  /**
   * Get registry statistics
   * @returns {object} Registry statistics
   */
  getStatistics() {
    const commands = this.getAll({ includeHidden: true });
    const categories = this.getByCategory();
    
    return {
      totalCommands: commands.length,
      visibleCommands: commands.filter(cmd => !cmd.hidden).length,
      hiddenCommands: commands.filter(cmd => cmd.hidden).length,
      totalAliases: this.aliases.size,
      categories: Object.keys(categories).length,
      categoryCounts: Object.fromEntries(
        Object.entries(categories).map(([cat, cmds]) => [cat, cmds.length])
      )
    };
  }

  /**
   * Clear all commands
   */
  clear() {
    const commandCount = this.commands.size;
    const aliasCount = this.aliases.size;
    
    this.commands.clear();
    this.aliases.clear();
    
    logger.debug('Command registry cleared', { commandCount, aliasCount });
  }

  /**
   * Export registry data
   * @returns {object} Exportable registry data
   */
  export() {
    return {
      commands: Object.fromEntries(this.commands),
      aliases: Object.fromEntries(this.aliases),
      statistics: this.getStatistics(),
      exportedAt: new Date()
    };
  }
}

export default CommandRegistry;
