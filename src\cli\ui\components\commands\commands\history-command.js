import UIHelpers from '../../../utils/ui-helpers.js';
import { ResponseFormatter } from '../../display/response-formatter.js';

/**
 * History command implementation
 */
export const HistoryCommand = {
  name: 'history',
  description: 'Show conversation history',
  usage: '/history [--limit=N] [--search=term] [--export=filename]',
  examples: [
    '/history',
    '/history --limit=10',
    '/history --search=javascript',
    '/history --export=my-chat.txt'
  ],
  category: 'conversation',
  aliases: ['hist', 'h'],

  /**
   * Execute history command
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   */
  async execute(args, context) {
    const messageManager = context.messageManager;
    
    if (!messageManager) {
      UIHelpers.print(UIHelpers.colors.error('Message manager not available'), 0, 1);
      return;
    }

    const messages = messageManager.getAllMessages();
    
    if (messages.length === 0) {
      UIHelpers.print(UIHelpers.colors.warning('No conversation history'), 0, 1);
      return;
    }

    // Handle export option
    if (args.named.export) {
      return this.exportHistory(messages, args.named.export, context);
    }

    // Handle search option
    if (args.named.search) {
      return this.searchHistory(args.named.search, context);
    }

    // Handle limit option
    const limit = args.named.limit ? parseInt(args.named.limit) : null;
    const displayMessages = limit ? messages.slice(-limit) : messages;

    // Display history
    this.displayHistory(displayMessages, messageManager.getStatistics(), limit);
  },

  /**
   * Display conversation history
   * @param {Array} messages - Messages to display
   * @param {object} stats - Conversation statistics
   * @param {number} limit - Display limit
   */
  displayHistory(messages, stats, limit) {
    // Header
    const header = limit 
      ? `Last ${Math.min(limit, messages.length)} Messages`
      : 'Complete Conversation History';
    
    UIHelpers.print(UIHelpers.colors.primary.bold(header), 0, 1);

    // Display messages
    ResponseFormatter.displayHistory(messages, {
      showIndex: true,
      wrapWidth: 70
    });

    // Display summary
    if (messages.length > 0) {
      UIHelpers.print(UIHelpers.createSeparator(), 0, 0);
      ResponseFormatter.displaySummary(messages, stats);
    }
  },

  /**
   * Search conversation history
   * @param {string} searchTerm - Term to search for
   * @param {object} context - Execution context
   */
  searchHistory(searchTerm, context) {
    const messageManager = context.messageManager;
    const results = messageManager.searchMessages(searchTerm, {
      caseSensitive: false,
      limit: 20
    });

    if (results.length === 0) {
      UIHelpers.print(
        UIHelpers.colors.warning(`No messages found containing "${searchTerm}"`),
        0, 1
      );
      return;
    }

    UIHelpers.print(
      UIHelpers.colors.primary.bold(`Search Results for "${searchTerm}" (${results.length} found):`),
      0, 1
    );

    results.forEach((message, index) => {
      const role = message.role === 'user' ? 'You' : 'Assistant';
      const roleColor = message.role === 'user' 
        ? UIHelpers.colors.secondary 
        : UIHelpers.colors.success;

      // Highlight search term in content
      const highlightedContent = this.highlightSearchTerm(message.content, searchTerm);
      const wrappedContent = UIHelpers.wrapText(highlightedContent, 70, '  ');

      UIHelpers.print(`${index + 1}. ${roleColor(role)}:`, 0, 0);
      UIHelpers.print(wrappedContent, 0, 0);
      
      if (message.timestamp) {
        UIHelpers.print(
          UIHelpers.colors.muted(`   ${new Date(message.timestamp).toLocaleString()}`),
          0, 1
        );
      } else {
        UIHelpers.print('', 0, 1);
      }
    });
  },

  /**
   * Export conversation history
   * @param {Array} messages - Messages to export
   * @param {string} filename - Export filename
   * @param {object} context - Execution context
   */
  async exportHistory(messages, filename, context) {
    try {
      const FileOperations = await import('../../../utils/file-operations.js');
      const configManager = await import('../../../../../config/config-manager.js');
      
      const activeProvider = configManager.default.getActiveProvider();
      const filepath = await FileOperations.default.saveConversation(
        messages,
        activeProvider.name,
        context.currentModel,
        filename
      );
      
      UIHelpers.print(
        UIHelpers.colors.success(`History exported to ${filepath}`),
        0, 1
      );

    } catch (error) {
      UIHelpers.print(
        UIHelpers.colors.error(`Failed to export history: ${error.message}`),
        0, 1
      );
    }
  },

  /**
   * Highlight search term in text
   * @param {string} text - Text to highlight
   * @param {string} searchTerm - Term to highlight
   * @returns {string} Text with highlighted search term
   */
  highlightSearchTerm(text, searchTerm) {
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, UIHelpers.colors.accent('$1'));
  },

  /**
   * Validate history command arguments
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   * @returns {boolean|string} True if valid, error message if invalid
   */
  validate(args, context) {
    // Check for positional arguments (not allowed)
    if (args.positional.length > 0) {
      return 'History command does not accept positional arguments. Use --limit, --search, or --export options.';
    }

    // Validate limit option
    if (args.named.limit) {
      const limit = parseInt(args.named.limit);
      if (isNaN(limit) || limit <= 0) {
        return 'Limit must be a positive number';
      }
      if (limit > 1000) {
        return 'Limit cannot exceed 1000 messages';
      }
    }

    // Validate search option
    if (args.named.search && args.named.search.trim().length === 0) {
      return 'Search term cannot be empty';
    }

    // Validate export option
    if (args.named.export && args.named.export.trim().length === 0) {
      return 'Export filename cannot be empty';
    }

    // Check for unknown flags
    const validFlags = [];
    const invalidFlags = args.flags.filter(flag => !validFlags.includes(flag));
    
    if (invalidFlags.length > 0) {
      return `Unknown flags: ${invalidFlags.join(', ')}`;
    }

    // Check for unknown named arguments
    const validNamed = ['limit', 'search', 'export'];
    const invalidNamed = Object.keys(args.named).filter(name => !validNamed.includes(name));
    
    if (invalidNamed.length > 0) {
      return `Unknown options: ${invalidNamed.join(', ')}. Valid options: ${validNamed.join(', ')}`;
    }

    return true;
  }
};

export default HistoryCommand;
