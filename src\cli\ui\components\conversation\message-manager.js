import logger from '../../../../utils/logger.js';

/**
 * Message manager for handling conversation state and operations
 */
export class MessageManager {
  constructor() {
    this.messages = [];
    this.maxMessages = 1000; // Prevent memory issues
    this.metadata = {
      startTime: new Date(),
      totalTokens: 0,
      messageCount: 0
    };
  }

  /**
   * Add a user message to the conversation
   * @param {string} content - Message content
   * @param {object} metadata - Optional metadata
   * @returns {object} Added message object
   */
  addUserMessage(content, metadata = {}) {
    const message = {
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
      id: this.generateMessageId(),
      ...metadata
    };

    this.messages.push(message);
    this.metadata.messageCount++;
    this.trimMessagesIfNeeded();

    logger.debug('User message added', { messageId: message.id, length: content.length });
    return message;
  }

  /**
   * Add an assistant message to the conversation
   * @param {string} content - Message content
   * @param {object} metadata - Optional metadata (usage, model, etc.)
   * @returns {object} Added message object
   */
  addAssistantMessage(content, metadata = {}) {
    const message = {
      role: 'assistant',
      content: content.trim(),
      timestamp: new Date(),
      id: this.generateMessageId(),
      ...metadata
    };

    this.messages.push(message);
    this.metadata.messageCount++;

    // Update token count if provided
    if (metadata.usage && metadata.usage.totalTokens) {
      this.metadata.totalTokens += metadata.usage.totalTokens;
    }

    this.trimMessagesIfNeeded();

    logger.debug('Assistant message added', { 
      messageId: message.id, 
      length: content.length,
      tokens: metadata.usage?.totalTokens 
    });

    return message;
  }

  /**
   * Get all messages
   * @returns {Array} Array of message objects
   */
  getAllMessages() {
    return [...this.messages];
  }

  /**
   * Get messages for API (without metadata)
   * @returns {Array} Array of messages formatted for API
   */
  getMessagesForApi() {
    return this.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  }

  /**
   * Get recent messages
   * @param {number} count - Number of recent messages to get
   * @returns {Array} Array of recent messages
   */
  getRecentMessages(count = 10) {
    return this.messages.slice(-count);
  }

  /**
   * Get messages by role
   * @param {string} role - Message role ('user' or 'assistant')
   * @returns {Array} Array of messages with specified role
   */
  getMessagesByRole(role) {
    return this.messages.filter(msg => msg.role === role);
  }

  /**
   * Get message by ID
   * @param {string} messageId - Message ID
   * @returns {object|null} Message object or null if not found
   */
  getMessageById(messageId) {
    return this.messages.find(msg => msg.id === messageId) || null;
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    const previousCount = this.messages.length;
    this.messages = [];
    this.metadata.messageCount = 0;
    this.metadata.totalTokens = 0;
    this.metadata.startTime = new Date();

    logger.info('Messages cleared', { previousCount });
  }

  /**
   * Remove a specific message
   * @param {string} messageId - Message ID to remove
   * @returns {boolean} True if message was removed
   */
  removeMessage(messageId) {
    const index = this.messages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      const removedMessage = this.messages.splice(index, 1)[0];
      this.metadata.messageCount--;
      
      logger.debug('Message removed', { messageId, role: removedMessage.role });
      return true;
    }
    return false;
  }

  /**
   * Update a message
   * @param {string} messageId - Message ID to update
   * @param {object} updates - Updates to apply
   * @returns {boolean} True if message was updated
   */
  updateMessage(messageId, updates) {
    const message = this.getMessageById(messageId);
    if (message) {
      Object.assign(message, updates, { updatedAt: new Date() });
      logger.debug('Message updated', { messageId, updates: Object.keys(updates) });
      return true;
    }
    return false;
  }

  /**
   * Get conversation statistics
   * @returns {object} Conversation statistics
   */
  getStatistics() {
    const userMessages = this.getMessagesByRole('user');
    const assistantMessages = this.getMessagesByRole('assistant');
    const duration = new Date() - this.metadata.startTime;

    return {
      totalMessages: this.messages.length,
      userMessages: userMessages.length,
      assistantMessages: assistantMessages.length,
      totalTokens: this.metadata.totalTokens,
      duration: this.formatDuration(duration),
      durationMs: duration,
      startTime: this.metadata.startTime,
      averageMessageLength: this.getAverageMessageLength(),
      longestMessage: this.getLongestMessage()
    };
  }

  /**
   * Search messages by content
   * @param {string} query - Search query
   * @param {object} options - Search options
   * @returns {Array} Array of matching messages
   */
  searchMessages(query, options = {}) {
    const defaultOptions = {
      caseSensitive: false,
      role: null, // 'user', 'assistant', or null for all
      limit: null,
      ...options
    };

    const searchTerm = defaultOptions.caseSensitive ? query : query.toLowerCase();
    
    let results = this.messages.filter(msg => {
      // Filter by role if specified
      if (defaultOptions.role && msg.role !== defaultOptions.role) {
        return false;
      }

      // Search in content
      const content = defaultOptions.caseSensitive ? msg.content : msg.content.toLowerCase();
      return content.includes(searchTerm);
    });

    // Apply limit if specified
    if (defaultOptions.limit) {
      results = results.slice(0, defaultOptions.limit);
    }

    return results;
  }

  /**
   * Export conversation data
   * @returns {object} Exportable conversation data
   */
  exportData() {
    return {
      messages: this.getAllMessages(),
      metadata: this.metadata,
      statistics: this.getStatistics(),
      exportedAt: new Date()
    };
  }

  /**
   * Import conversation data
   * @param {object} data - Conversation data to import
   */
  importData(data) {
    if (data.messages && Array.isArray(data.messages)) {
      this.messages = data.messages;
      this.metadata.messageCount = this.messages.length;
    }

    if (data.metadata) {
      this.metadata = { ...this.metadata, ...data.metadata };
    }

    logger.info('Conversation data imported', { messageCount: this.messages.length });
  }

  /**
   * Generate a unique message ID
   * @returns {string} Unique message ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Trim messages if exceeding maximum
   */
  trimMessagesIfNeeded() {
    if (this.messages.length > this.maxMessages) {
      const removed = this.messages.splice(0, this.messages.length - this.maxMessages);
      logger.warn('Messages trimmed due to limit', { 
        removedCount: removed.length, 
        maxMessages: this.maxMessages 
      });
    }
  }

  /**
   * Get average message length
   * @returns {number} Average message length
   */
  getAverageMessageLength() {
    if (this.messages.length === 0) return 0;
    
    const totalLength = this.messages.reduce((sum, msg) => sum + msg.content.length, 0);
    return Math.round(totalLength / this.messages.length);
  }

  /**
   * Get longest message
   * @returns {object|null} Longest message object
   */
  getLongestMessage() {
    if (this.messages.length === 0) return null;
    
    return this.messages.reduce((longest, msg) => 
      msg.content.length > longest.content.length ? msg : longest
    );
  }

  /**
   * Format duration in human-readable format
   * @param {number} ms - Duration in milliseconds
   * @returns {string} Formatted duration
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

export default MessageManager;
