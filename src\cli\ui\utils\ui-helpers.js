import chalk from 'chalk';
import boxen from 'boxen';
import wordWrap from 'word-wrap';

/**
 * UI Helper utilities for consistent styling and formatting
 */
export class UIHelpers {
  /**
   * Color scheme for consistent theming
   */
  static colors = {
    primary: chalk.cyan,
    secondary: chalk.blue,
    success: chalk.green,
    warning: chalk.yellow,
    error: chalk.red,
    muted: chalk.gray,
    text: chalk.white,
    accent: chalk.magenta
  };

  /**
   * Create a styled header box
   * @param {string} title - Main title text
   * @param {string} subtitle - Optional subtitle text
   * @param {object} options - Styling options
   * @returns {string} Formatted header
   */
  static createHeader(title, subtitle = '', options = {}) {
    const defaultOptions = {
      padding: 1,
      borderStyle: 'round',
      borderColor: 'cyan',
      textAlignment: 'center'
    };

    const boxOptions = { ...defaultOptions, ...options };
    
    let content = this.colors.primary.bold(title);
    if (subtitle) {
      content += '\n' + this.colors.muted(subtitle);
    }

    return boxen(content, boxOptions);
  }

  /**
   * Create a styled info box
   * @param {string} content - Content to display
   * @param {string} type - Box type (info, success, warning, error)
   * @returns {string} Formatted info box
   */
  static createInfoBox(content, type = 'info') {
    const typeConfig = {
      info: { color: 'blue', icon: 'ℹ' },
      success: { color: 'green', icon: '✓' },
      warning: { color: 'yellow', icon: '⚠' },
      error: { color: 'red', icon: '✗' }
    };

    const config = typeConfig[type] || typeConfig.info;
    
    return boxen(content, {
      padding: { top: 0, bottom: 0, left: 1, right: 1 },
      borderStyle: 'round',
      borderColor: config.color,
      textAlignment: 'left'
    });
  }

  /**
   * Format text with word wrapping
   * @param {string} text - Text to wrap
   * @param {number} width - Maximum width
   * @param {string} indent - Indentation string
   * @returns {string} Wrapped text
   */
  static wrapText(text, width = 80, indent = '') {
    return wordWrap(text, { width, indent });
  }

  /**
   * Create a separator line
   * @param {number} length - Length of separator
   * @param {string} char - Character to use
   * @returns {string} Separator line
   */
  static createSeparator(length = 50, char = '─') {
    return this.colors.muted(char.repeat(length));
  }

  /**
   * Format a list of items
   * @param {Array} items - Items to format
   * @param {string} bullet - Bullet character
   * @param {string} indent - Indentation
   * @returns {string} Formatted list
   */
  static formatList(items, bullet = '•', indent = '  ') {
    return items
      .map(item => `${indent}${this.colors.muted(bullet)} ${item}`)
      .join('\n');
  }

  /**
   * Clear screen and move cursor to top
   */
  static clearScreen() {
    console.clear();
  }

  /**
   * Print with spacing
   * @param {string} content - Content to print
   * @param {number} spaceBefore - Lines before content
   * @param {number} spaceAfter - Lines after content
   */
  static print(content, spaceBefore = 0, spaceAfter = 1) {
    if (spaceBefore > 0) {
      console.log('\n'.repeat(spaceBefore - 1));
    }
    console.log(content);
    if (spaceAfter > 0) {
      console.log('\n'.repeat(spaceAfter - 1));
    }
  }

  /**
   * Format token usage information
   * @param {object} usage - Token usage object
   * @returns {string} Formatted token info
   */
  static formatTokenUsage(usage) {
    if (!usage) return '';
    
    return this.colors.muted(
      `[Tokens: ${usage.totalTokens} (${usage.promptTokens} + ${usage.completionTokens})]`
    );
  }

  /**
   * Create a progress indicator
   * @param {string} message - Progress message
   * @returns {string} Formatted progress indicator
   */
  static createProgressIndicator(message) {
    return `${this.colors.primary('▶')} ${this.colors.text(message)}`;
  }

  /**
   * Format command help text
   * @param {Array} commands - Array of command objects {name, description}
   * @returns {string} Formatted help text
   */
  static formatCommandHelp(commands) {
    const maxNameLength = Math.max(...commands.map(cmd => cmd.name.length));
    
    return commands
      .map(cmd => {
        const name = this.colors.secondary(`/${cmd.name.padEnd(maxNameLength)}`);
        const desc = this.colors.muted(cmd.description);
        return `  ${name} - ${desc}`;
      })
      .join('\n');
  }

  /**
   * Format role-based message display
   * @param {string} role - Message role (user, assistant)
   * @param {string} content - Message content
   * @param {number} index - Message index
   * @returns {string} Formatted message
   */
  static formatMessage(role, content, index = null) {
    const roleColors = {
      user: this.colors.secondary,
      assistant: this.colors.success
    };

    const roleNames = {
      user: 'You',
      assistant: 'Assistant'
    };

    const roleColor = roleColors[role] || this.colors.text;
    const roleName = roleNames[role] || role;
    const wrappedContent = this.wrapText(content, 70, '  ');

    let output = '';
    if (index !== null) {
      output += `${index}. `;
    }
    output += `${roleColor(roleName)}:\n${wrappedContent}`;

    return output;
  }
}

export default UIHelpers;
