import UIHelpers from '../../../utils/ui-helpers.js';
import { MessageInput } from '../../input/message-input.js';

/**
 * Clear command implementation
 */
export const ClearCommand = {
  name: 'clear',
  description: 'Clear the conversation history',
  usage: '/clear [--force]',
  examples: [
    '/clear',
    '/clear --force'
  ],
  category: 'conversation',
  aliases: ['c', 'reset'],

  /**
   * Execute clear command
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   */
  async execute(args, context) {
    const force = args.flags.includes('force') || args.named.force;

    // Check if there are messages to clear
    if (!context.messageManager || context.messageManager.getAllMessages().length === 0) {
      UIHelpers.print(UIHelpers.colors.warning('No conversation history to clear'), 0, 1);
      return;
    }

    // Ask for confirmation unless force flag is used
    if (!force) {
      const confirmed = await MessageInput.getConfirmation(
        'Clear the conversation history?',
        false
      );

      if (!confirmed) {
        UIHelpers.print(UIHelpers.colors.muted('Clear operation cancelled'), 0, 1);
        return;
      }
    }

    // Clear the conversation
    const messageCount = context.messageManager.getAllMessages().length;
    context.messageManager.clearMessages();

    // Update conversation state if available
    if (context.conversationState) {
      context.conversationState.recordActivity('conversationCleared', {
        messageCount,
        forced: force
      });
    }

    // Refresh the display
    if (context.showChatHeader) {
      context.showChatHeader();
    }

    if (context.showInstructions) {
      context.showInstructions();
    }

    UIHelpers.print(
      UIHelpers.colors.success(`Conversation cleared (${messageCount} messages removed)`),
      0, 1
    );
  },

  /**
   * Validate clear command arguments
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   * @returns {boolean|string} True if valid, error message if invalid
   */
  validate(args, context) {
    // Check for unknown flags
    const validFlags = ['force'];
    const invalidFlags = args.flags.filter(flag => !validFlags.includes(flag));
    
    if (invalidFlags.length > 0) {
      return `Unknown flags: ${invalidFlags.join(', ')}. Valid flags: ${validFlags.join(', ')}`;
    }

    // Check for positional arguments (not allowed)
    if (args.positional.length > 0) {
      return 'Clear command does not accept positional arguments';
    }

    return true;
  }
};

export default ClearCommand;
