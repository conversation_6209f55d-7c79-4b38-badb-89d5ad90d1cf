import UIHelpers from '../../../utils/ui-helpers.js';

/**
 * Model command implementation
 */
export const ModelCommand = {
  name: 'model',
  description: 'Change the current AI model',
  usage: '/model [model-name]',
  examples: [
    '/model',
    '/model gpt-4',
    '/model claude-3-sonnet'
  ],
  category: 'configuration',
  aliases: ['m'],

  /**
   * Execute model command
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   */
  async execute(args, context) {
    const modelName = args.positional[0];

    if (modelName) {
      // Set specific model
      return this.setModel(modelName, context);
    } else {
      // Show model selection interface
      return this.showModelSelector(context);
    }
  },

  /**
   * Set a specific model
   * @param {string} modelName - Model name to set
   * @param {object} context - Execution context
   */
  async setModel(modelName, context) {
    try {
      // Validate model availability (if provider supports it)
      if (context.providerInterface && context.providerInterface.getAvailableModels) {
        const availableModels = await context.providerInterface.getAvailableModels();
        const modelExists = availableModels.some(model => 
          model.name === modelName || model.id === modelName
        );

        if (!modelExists) {
          UIHelpers.print(
            UIHelpers.colors.error(`Model '${modelName}' is not available`),
            0, 0
          );
          UIHelpers.print(
            UIHelpers.colors.muted('Use /model without arguments to see available models'),
            0, 1
          );
          return;
        }
      }

      const previousModel = context.currentModel;
      context.currentModel = modelName;

      // Update conversation state
      if (context.conversationState) {
        context.conversationState.updateModel(modelName);
      }

      UIHelpers.print(
        UIHelpers.colors.success(`Model changed from ${previousModel} to ${modelName}`),
        0, 1
      );

      // Refresh header
      if (context.showChatHeader) {
        context.showChatHeader();
      }

    } catch (error) {
      UIHelpers.print(
        UIHelpers.colors.error(`Failed to change model: ${error.message}`),
        0, 1
      );
    }
  },

  /**
   * Show model selection interface
   * @param {object} context - Execution context
   */
  async showModelSelector(context) {
    try {
      // Use existing model selector component
      const { ModelSelector } = await import('../../model-selector.js');
      const configManager = await import('../../../../../config/config-manager.js');
      
      const activeProvider = configManager.default.getActiveProvider();
      const newModel = await ModelSelector.quickSelectModel(activeProvider.name);
      
      if (newModel && newModel !== context.currentModel) {
        const previousModel = context.currentModel;
        context.currentModel = newModel;
        
        // Update conversation state
        if (context.conversationState) {
          context.conversationState.updateModel(newModel);
        }
        
        UIHelpers.print(
          UIHelpers.colors.success(`Model changed from ${previousModel} to ${newModel}`),
          0, 1
        );
        
        // Refresh header
        if (context.showChatHeader) {
          context.showChatHeader();
        }
      } else if (newModel === context.currentModel) {
        UIHelpers.print(
          UIHelpers.colors.muted(`Already using ${newModel}`),
          0, 1
        );
      }

    } catch (error) {
      UIHelpers.print(
        UIHelpers.colors.error(`Failed to show model selector: ${error.message}`),
        0, 1
      );
    }
  },

  /**
   * Validate model command arguments
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   * @returns {boolean|string} True if valid, error message if invalid
   */
  validate(args, context) {
    // Model command accepts optional model name
    if (args.positional.length > 1) {
      return 'Model command accepts only one argument (model name)';
    }

    // Check for unknown flags
    if (args.flags.length > 0) {
      return `Model command does not accept flags: ${args.flags.join(', ')}`;
    }

    return true;
  }
};

export default ModelCommand;
