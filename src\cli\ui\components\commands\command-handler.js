import { CommandRegistry } from './command-registry.js';
import UIHelpers from '../../utils/ui-helpers.js';
import logger from '../../../../utils/logger.js';

/**
 * Command handler for processing and executing chat commands
 */
export class CommandHandler {
  constructor() {
    this.registry = new CommandRegistry();
    this.context = null;
    this.middleware = [];
  }

  /**
   * Set the execution context
   * @param {object} context - Execution context (chat interface instance, etc.)
   */
  setContext(context) {
    this.context = context;
  }

  /**
   * Add middleware function
   * @param {function} middleware - Middleware function
   */
  addMiddleware(middleware) {
    if (typeof middleware !== 'function') {
      throw new Error('Middleware must be a function');
    }
    this.middleware.push(middleware);
  }

  /**
   * Register a command
   * @param {string} name - Command name
   * @param {object} command - Command object
   */
  register(name, command) {
    this.registry.register(name, command);
  }

  /**
   * Process and execute a command
   * @param {string} input - Command input (including /)
   * @returns {Promise<object>} Execution result
   */
  async execute(input) {
    try {
      // Parse command input
      const parsed = this.parseCommand(input);
      
      if (!parsed) {
        return this.createResult(false, 'Invalid command format');
      }

      // Get command from registry
      const command = this.registry.get(parsed.name);
      
      if (!command) {
        return this.handleUnknownCommand(parsed.name);
      }

      // Run middleware
      const middlewareResult = await this.runMiddleware(parsed, command);
      if (!middlewareResult.success) {
        return middlewareResult;
      }

      // Validate command arguments if validator exists
      if (command.validate) {
        const validationResult = await command.validate(parsed.args, this.context);
        if (validationResult !== true) {
          return this.createResult(false, validationResult || 'Invalid command arguments');
        }
      }

      // Execute command
      logger.debug('Executing command', { name: parsed.name, args: parsed.args });
      
      const result = await command.execute(parsed.args, this.context);
      
      return this.createResult(true, 'Command executed successfully', result);

    } catch (error) {
      logger.error('Command execution error', { error: error.message, input });
      return this.createResult(false, `Command execution failed: ${error.message}`);
    }
  }

  /**
   * Parse command input
   * @param {string} input - Raw command input
   * @returns {object|null} Parsed command object
   */
  parseCommand(input) {
    if (!input || !input.startsWith('/')) {
      return null;
    }

    const trimmed = input.slice(1).trim();
    if (!trimmed) {
      return null;
    }

    // Split command and arguments
    const parts = trimmed.split(/\s+/);
    const name = parts[0].toLowerCase();
    const args = parts.slice(1);

    // Parse arguments into key-value pairs and flags
    const parsedArgs = this.parseArguments(args);

    return {
      name,
      args: parsedArgs,
      raw: trimmed,
      original: input
    };
  }

  /**
   * Parse command arguments
   * @param {Array} args - Array of argument strings
   * @returns {object} Parsed arguments object
   */
  parseArguments(args) {
    const parsed = {
      positional: [],
      named: {},
      flags: []
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];

      if (arg.startsWith('--')) {
        // Named argument (--key=value or --key value)
        const keyValue = arg.slice(2);
        const equalIndex = keyValue.indexOf('=');
        
        if (equalIndex !== -1) {
          // --key=value format
          const key = keyValue.slice(0, equalIndex);
          const value = keyValue.slice(equalIndex + 1);
          parsed.named[key] = value;
        } else {
          // --key value format (check next argument)
          const key = keyValue;
          if (i + 1 < args.length && !args[i + 1].startsWith('-')) {
            parsed.named[key] = args[i + 1];
            i++; // Skip next argument
          } else {
            // Flag without value
            parsed.flags.push(key);
          }
        }
      } else if (arg.startsWith('-')) {
        // Short flag
        const flag = arg.slice(1);
        parsed.flags.push(flag);
      } else {
        // Positional argument
        parsed.positional.push(arg);
      }
    }

    return parsed;
  }

  /**
   * Run middleware functions
   * @param {object} parsed - Parsed command
   * @param {object} command - Command object
   * @returns {Promise<object>} Middleware result
   */
  async runMiddleware(parsed, command) {
    for (const middleware of this.middleware) {
      try {
        const result = await middleware(parsed, command, this.context);
        if (result && !result.success) {
          return result;
        }
      } catch (error) {
        logger.error('Middleware error', { error: error.message });
        return this.createResult(false, `Middleware error: ${error.message}`);
      }
    }

    return this.createResult(true);
  }

  /**
   * Handle unknown command
   * @param {string} commandName - Unknown command name
   * @returns {object} Result object
   */
  handleUnknownCommand(commandName) {
    // Get suggestions
    const suggestions = this.registry.getSuggestions(commandName, 3);
    
    let message = `Unknown command: /${commandName}`;
    
    if (suggestions.length > 0) {
      message += `\nDid you mean: ${suggestions.map(s => `/${s}`).join(', ')}?`;
    }
    
    message += '\nType /help for available commands';

    UIHelpers.print(UIHelpers.colors.error(message), 0, 1);
    
    return this.createResult(false, message);
  }

  /**
   * Get command help
   * @param {string} commandName - Optional specific command name
   * @returns {object} Help information
   */
  getHelp(commandName = null) {
    if (commandName) {
      const command = this.registry.get(commandName);
      if (!command) {
        return {
          success: false,
          message: `Command not found: /${commandName}`
        };
      }

      return {
        success: true,
        command: command,
        help: this.formatCommandHelp(command)
      };
    }

    // Return all commands grouped by category
    const categories = this.registry.getByCategory();
    return {
      success: true,
      categories: categories,
      help: this.formatAllCommandsHelp(categories)
    };
  }

  /**
   * Format help for a specific command
   * @param {object} command - Command object
   * @returns {string} Formatted help text
   */
  formatCommandHelp(command) {
    let help = `/${command.name} - ${command.description}\n`;
    
    if (command.usage) {
      help += `Usage: ${command.usage}\n`;
    }

    if (command.aliases && command.aliases.length > 0) {
      help += `Aliases: ${command.aliases.map(a => `/${a}`).join(', ')}\n`;
    }

    if (command.examples && command.examples.length > 0) {
      help += `Examples:\n`;
      command.examples.forEach(example => {
        help += `  ${example}\n`;
      });
    }

    return help.trim();
  }

  /**
   * Format help for all commands
   * @param {object} categories - Commands grouped by category
   * @returns {string} Formatted help text
   */
  formatAllCommandsHelp(categories) {
    let help = 'Available Commands:\n\n';

    Object.entries(categories).forEach(([category, commands]) => {
      help += `${category.charAt(0).toUpperCase() + category.slice(1)}:\n`;
      commands.forEach(cmd => {
        help += `  /${cmd.name.padEnd(12)} - ${cmd.description}\n`;
      });
      help += '\n';
    });

    help += 'Type /help <command> for detailed information about a specific command.';
    
    return help;
  }

  /**
   * Get command suggestions for autocomplete
   * @param {string} partial - Partial command input
   * @returns {Array} Array of suggestions
   */
  getSuggestions(partial) {
    if (!partial.startsWith('/')) {
      return [];
    }

    const commandPart = partial.slice(1);
    return this.registry.getSuggestions(commandPart).map(name => `/${name}`);
  }

  /**
   * Check if input is a command
   * @param {string} input - Input to check
   * @returns {boolean} True if input is a command
   */
  isCommand(input) {
    return input && input.trim().startsWith('/');
  }

  /**
   * Create a standardized result object
   * @param {boolean} success - Success status
   * @param {string} message - Result message
   * @param {*} data - Additional result data
   * @returns {object} Result object
   */
  createResult(success, message = '', data = null) {
    return {
      success,
      message,
      data,
      timestamp: new Date()
    };
  }

  /**
   * Get registry instance
   * @returns {CommandRegistry} Registry instance
   */
  getRegistry() {
    return this.registry;
  }

  /**
   * Get handler statistics
   * @returns {object} Handler statistics
   */
  getStatistics() {
    return {
      ...this.registry.getStatistics(),
      middlewareCount: this.middleware.length,
      hasContext: !!this.context
    };
  }
}

export default CommandHandler;
