import { EventEmitter } from 'events';
import logger from '../../../../utils/logger.js';

/**
 * Conversation state manager with event handling
 */
export class ConversationState extends EventEmitter {
  constructor() {
    super();
    this.state = {
      isActive: false,
      isProcessing: false,
      currentProvider: null,
      currentModel: null,
      lastActivity: null,
      sessionId: this.generateSessionId(),
      startTime: new Date(),
      settings: {
        autoSave: false,
        maxHistory: 100,
        showTokenCount: true,
        showTimestamps: false
      }
    };

    this.history = [];
    this.setupEventHandlers();
  }

  /**
   * Initialize conversation state
   * @param {string} provider - Provider name
   * @param {string} model - Model name
   * @param {object} settings - Optional settings
   */
  initialize(provider, model, settings = {}) {
    this.state.currentProvider = provider;
    this.state.currentModel = model;
    this.state.isActive = true;
    this.state.lastActivity = new Date();
    this.state.settings = { ...this.state.settings, ...settings };

    this.emit('initialized', {
      provider,
      model,
      sessionId: this.state.sessionId
    });

    logger.info('Conversation state initialized', {
      provider,
      model,
      sessionId: this.state.sessionId
    });
  }

  /**
   * Start processing state
   */
  startProcessing() {
    if (this.state.isProcessing) {
      logger.warn('Already processing, ignoring start request');
      return;
    }

    this.state.isProcessing = true;
    this.state.lastActivity = new Date();
    this.emit('processingStarted');

    logger.debug('Processing started');
  }

  /**
   * Stop processing state
   */
  stopProcessing() {
    if (!this.state.isProcessing) {
      logger.warn('Not processing, ignoring stop request');
      return;
    }

    this.state.isProcessing = false;
    this.state.lastActivity = new Date();
    this.emit('processingStopped');

    logger.debug('Processing stopped');
  }

  /**
   * Update current model
   * @param {string} newModel - New model name
   */
  updateModel(newModel) {
    const previousModel = this.state.currentModel;
    this.state.currentModel = newModel;
    this.state.lastActivity = new Date();

    this.emit('modelChanged', {
      previousModel,
      newModel,
      provider: this.state.currentProvider
    });

    logger.info('Model changed', { previousModel, newModel });
  }

  /**
   * Update provider
   * @param {string} newProvider - New provider name
   * @param {string} newModel - New model name
   */
  updateProvider(newProvider, newModel) {
    const previousProvider = this.state.currentProvider;
    const previousModel = this.state.currentModel;

    this.state.currentProvider = newProvider;
    this.state.currentModel = newModel;
    this.state.lastActivity = new Date();

    this.emit('providerChanged', {
      previousProvider,
      newProvider,
      previousModel,
      newModel
    });

    logger.info('Provider changed', { previousProvider, newProvider, newModel });
  }

  /**
   * Update settings
   * @param {object} newSettings - Settings to update
   */
  updateSettings(newSettings) {
    const previousSettings = { ...this.state.settings };
    this.state.settings = { ...this.state.settings, ...newSettings };
    this.state.lastActivity = new Date();

    this.emit('settingsChanged', {
      previousSettings,
      newSettings: this.state.settings
    });

    logger.debug('Settings updated', { newSettings });
  }

  /**
   * Record user activity
   * @param {string} activity - Activity type
   * @param {object} data - Activity data
   */
  recordActivity(activity, data = {}) {
    const activityRecord = {
      type: activity,
      timestamp: new Date(),
      data,
      sessionId: this.state.sessionId
    };

    this.history.push(activityRecord);
    this.state.lastActivity = new Date();

    // Trim history if needed
    if (this.history.length > this.state.settings.maxHistory) {
      this.history = this.history.slice(-this.state.settings.maxHistory);
    }

    this.emit('activityRecorded', activityRecord);
    logger.debug('Activity recorded', { activity, data });
  }

  /**
   * Get current state
   * @returns {object} Current state
   */
  getCurrentState() {
    return {
      ...this.state,
      uptime: new Date() - this.state.startTime,
      historyCount: this.history.length
    };
  }

  /**
   * Get activity history
   * @param {number} limit - Maximum number of activities to return
   * @returns {Array} Activity history
   */
  getActivityHistory(limit = null) {
    const history = [...this.history];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Check if conversation is active
   * @returns {boolean} True if active
   */
  isActive() {
    return this.state.isActive;
  }

  /**
   * Check if currently processing
   * @returns {boolean} True if processing
   */
  isProcessing() {
    return this.state.isProcessing;
  }

  /**
   * Get session information
   * @returns {object} Session information
   */
  getSessionInfo() {
    return {
      sessionId: this.state.sessionId,
      startTime: this.state.startTime,
      lastActivity: this.state.lastActivity,
      uptime: new Date() - this.state.startTime,
      provider: this.state.currentProvider,
      model: this.state.currentModel,
      isActive: this.state.isActive,
      isProcessing: this.state.isProcessing
    };
  }

  /**
   * Pause conversation
   */
  pause() {
    if (!this.state.isActive) {
      logger.warn('Conversation already paused');
      return;
    }

    this.state.isActive = false;
    this.recordActivity('paused');
    this.emit('paused');

    logger.info('Conversation paused');
  }

  /**
   * Resume conversation
   */
  resume() {
    if (this.state.isActive) {
      logger.warn('Conversation already active');
      return;
    }

    this.state.isActive = true;
    this.state.lastActivity = new Date();
    this.recordActivity('resumed');
    this.emit('resumed');

    logger.info('Conversation resumed');
  }

  /**
   * End conversation
   */
  end() {
    this.state.isActive = false;
    this.state.isProcessing = false;
    this.recordActivity('ended');
    this.emit('ended');

    logger.info('Conversation ended', {
      sessionId: this.state.sessionId,
      duration: new Date() - this.state.startTime
    });
  }

  /**
   * Reset conversation state
   */
  reset() {
    const oldSessionId = this.state.sessionId;
    
    this.state = {
      isActive: false,
      isProcessing: false,
      currentProvider: null,
      currentModel: null,
      lastActivity: null,
      sessionId: this.generateSessionId(),
      startTime: new Date(),
      settings: this.state.settings // Preserve settings
    };

    this.history = [];
    this.emit('reset', { oldSessionId, newSessionId: this.state.sessionId });

    logger.info('Conversation state reset', { oldSessionId, newSessionId: this.state.sessionId });
  }

  /**
   * Export state data
   * @returns {object} Exportable state data
   */
  exportState() {
    return {
      state: this.getCurrentState(),
      history: this.getActivityHistory(),
      exportedAt: new Date()
    };
  }

  /**
   * Import state data
   * @param {object} data - State data to import
   */
  importState(data) {
    if (data.state) {
      this.state = { ...this.state, ...data.state };
    }

    if (data.history && Array.isArray(data.history)) {
      this.history = data.history;
    }

    this.emit('stateImported', data);
    logger.info('State imported');
  }

  /**
   * Generate a unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Setup internal event handlers
   */
  setupEventHandlers() {
    this.on('initialized', () => {
      this.recordActivity('initialized', {
        provider: this.state.currentProvider,
        model: this.state.currentModel
      });
    });

    this.on('modelChanged', (data) => {
      this.recordActivity('modelChanged', data);
    });

    this.on('providerChanged', (data) => {
      this.recordActivity('providerChanged', data);
    });

    this.on('processingStarted', () => {
      this.recordActivity('processingStarted');
    });

    this.on('processingStopped', () => {
      this.recordActivity('processingStopped');
    });
  }

  /**
   * Get state summary for display
   * @returns {object} State summary
   */
  getStateSummary() {
    const uptime = new Date() - this.state.startTime;
    const lastActivityAge = this.state.lastActivity 
      ? new Date() - this.state.lastActivity 
      : null;

    return {
      status: this.state.isActive ? 'Active' : 'Inactive',
      processing: this.state.isProcessing ? 'Processing' : 'Idle',
      provider: this.state.currentProvider,
      model: this.state.currentModel,
      uptime: this.formatDuration(uptime),
      lastActivity: lastActivityAge ? this.formatDuration(lastActivityAge) + ' ago' : 'Never',
      activities: this.history.length
    };
  }

  /**
   * Format duration in human-readable format
   * @param {number} ms - Duration in milliseconds
   * @returns {string} Formatted duration
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

export default ConversationState;
