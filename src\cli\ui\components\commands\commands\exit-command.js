import UIHelpers from '../../../utils/ui-helpers.js';

/**
 * Exit command implementation
 */
export const ExitCommand = {
  name: 'exit',
  description: 'Exit the chat interface',
  usage: '/exit [--force]',
  examples: [
    '/exit',
    '/exit --force'
  ],
  category: 'general',
  aliases: ['quit', 'q', 'bye'],

  /**
   * Execute exit command
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   */
  async execute(args, context) {
    const force = args.flags.includes('force') || args.named.force;

    // Record exit activity
    if (context.conversationState) {
      context.conversationState.recordActivity('exitRequested', {
        forced: force,
        messageCount: context.messageManager?.getAllMessages().length || 0
      });
    }

    // Show goodbye message
    this.showGoodbyeMessage(context, force);

    // End conversation state
    if (context.conversationState) {
      context.conversationState.end();
    }

    // Set running flag to false to exit main loop
    if (context.setRunning) {
      context.setRunning(false);
    } else if (context.isRunning !== undefined) {
      context.isRunning = false;
    }

    return { exit: true };
  },

  /**
   * Show goodbye message
   * @param {object} context - Execution context
   * @param {boolean} force - Whether exit was forced
   */
  showGoodbyeMessage(context, force) {
    const stats = context.messageManager?.getStatistics();
    
    if (stats && stats.totalMessages > 0 && !force) {
      // Show session summary
      const summary = [
        UIHelpers.colors.primary.bold('Session Summary:'),
        '',
        `Messages exchanged: ${stats.totalMessages}`,
        `Session duration: ${stats.duration}`,
        `Average message length: ${stats.averageMessageLength} characters`
      ];

      if (stats.totalTokens > 0) {
        summary.push(`Total tokens used: ${stats.totalTokens}`);
      }

      UIHelpers.print(summary.join('\n'), 0, 1);
    }

    // Goodbye message
    const goodbyeMessages = [
      'Goodbye! Thanks for using LLM CLI.',
      'See you next time!',
      'Happy coding!',
      'Until next time!',
      'Farewell!'
    ];

    const randomMessage = goodbyeMessages[Math.floor(Math.random() * goodbyeMessages.length)];
    
    const farewell = UIHelpers.createInfoBox(
      UIHelpers.colors.primary(randomMessage),
      'info'
    );

    UIHelpers.print(farewell, 0, 1);
  },

  /**
   * Validate exit command arguments
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   * @returns {boolean|string} True if valid, error message if invalid
   */
  validate(args, context) {
    // Check for unknown flags
    const validFlags = ['force'];
    const invalidFlags = args.flags.filter(flag => !validFlags.includes(flag));
    
    if (invalidFlags.length > 0) {
      return `Unknown flags: ${invalidFlags.join(', ')}. Valid flags: ${validFlags.join(', ')}`;
    }

    // Check for positional arguments (not allowed)
    if (args.positional.length > 0) {
      return 'Exit command does not accept positional arguments';
    }

    return true;
  }
};

export default ExitCommand;
