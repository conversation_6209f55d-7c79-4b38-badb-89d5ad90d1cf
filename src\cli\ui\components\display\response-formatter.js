import UIHelpers from '../../utils/ui-helpers.js';

/**
 * Response formatter component for displaying LLM responses
 */
export class ResponseFormatter {
  /**
   * Display a formatted LLM response
   * @param {object} response - Response object from LLM
   * @param {object} options - Formatting options
   */
  static display(response, options = {}) {
    const defaultOptions = {
      showTokenUsage: true,
      showTimestamp: false,
      wrapWidth: 80,
      showRole: true,
      addSpacing: true,
      ...options
    };

    if (defaultOptions.addSpacing) {
      UIHelpers.print('', 0, 0);
    }

    // Display role label
    if (defaultOptions.showRole) {
      UIHelpers.print(UIHelpers.colors.success('Assistant:'), 0, 0);
    }

    // Format and display content
    const wrappedContent = UIHelpers.wrapText(
      response.content, 
      defaultOptions.wrapWidth, 
      ''
    );
    
    UIHelpers.print(wrappedContent, 0, 0);

    // Display metadata
    this.displayMetadata(response, defaultOptions);

    if (defaultOptions.addSpacing) {
      UIHelpers.print('', 0, 1);
    }
  }

  /**
   * Display response metadata (tokens, timing, etc.)
   * @param {object} response - Response object
   * @param {object} options - Display options
   */
  static displayMetadata(response, options) {
    const metadata = [];

    // Token usage
    if (options.showTokenUsage && response.usage) {
      const tokenInfo = UIHelpers.formatTokenUsage(response.usage);
      if (tokenInfo) {
        metadata.push(tokenInfo);
      }
    }

    // Timestamp
    if (options.showTimestamp) {
      const timestamp = new Date().toLocaleTimeString();
      metadata.push(UIHelpers.colors.muted(`[${timestamp}]`));
    }

    // Model information
    if (response.model) {
      metadata.push(UIHelpers.colors.muted(`[${response.model}]`));
    }

    // Display metadata if any
    if (metadata.length > 0) {
      UIHelpers.print(metadata.join(' '), 0, 0);
    }
  }

  /**
   * Display a streaming response (for real-time updates)
   * @param {string} content - Current content
   * @param {boolean} isComplete - Whether streaming is complete
   */
  static displayStreaming(content, isComplete = false) {
    // Clear previous content and redisplay
    process.stdout.write('\r\x1b[K'); // Clear current line
    process.stdout.write('\x1b[1A'); // Move up one line
    
    const wrappedContent = UIHelpers.wrapText(content, 80, '');
    console.log(wrappedContent);
    
    if (!isComplete) {
      process.stdout.write(UIHelpers.colors.muted('▋')); // Cursor indicator
    }
  }

  /**
   * Display an error response
   * @param {Error} error - Error object
   * @param {object} options - Display options
   */
  static displayError(error, options = {}) {
    const defaultOptions = {
      showDetails: false,
      addSpacing: true,
      ...options
    };

    if (defaultOptions.addSpacing) {
      UIHelpers.print('', 0, 0);
    }

    UIHelpers.print(UIHelpers.colors.error('Error:'), 0, 0);
    UIHelpers.print(UIHelpers.colors.error(error.message), 0, 0);

    if (defaultOptions.showDetails && error.stack) {
      UIHelpers.print(UIHelpers.colors.muted('Details:'), 0, 0);
      UIHelpers.print(UIHelpers.colors.muted(error.stack), 0, 0);
    }

    if (defaultOptions.addSpacing) {
      UIHelpers.print('', 0, 1);
    }
  }

  /**
   * Display a thinking/loading indicator
   * @param {string} message - Loading message
   */
  static displayThinking(message = 'Thinking...') {
    UIHelpers.print('', 0, 0);
    UIHelpers.print(UIHelpers.colors.muted(`${message}`), 0, 0);
  }

  /**
   * Display conversation history
   * @param {Array} messages - Array of message objects
   * @param {object} options - Display options
   */
  static displayHistory(messages, options = {}) {
    const defaultOptions = {
      showIndex: true,
      wrapWidth: 70,
      maxMessages: null,
      ...options
    };

    if (!messages || messages.length === 0) {
      UIHelpers.print(UIHelpers.colors.warning('No conversation history'), 0, 1);
      return;
    }

    UIHelpers.print(UIHelpers.colors.primary.bold('Conversation History:'), 0, 1);

    const messagesToShow = defaultOptions.maxMessages 
      ? messages.slice(-defaultOptions.maxMessages)
      : messages;

    messagesToShow.forEach((msg, index) => {
      const actualIndex = defaultOptions.maxMessages 
        ? messages.length - messagesToShow.length + index + 1
        : index + 1;

      const formattedMessage = UIHelpers.formatMessage(
        msg.role, 
        msg.content, 
        defaultOptions.showIndex ? actualIndex : null
      );

      UIHelpers.print(formattedMessage, 0, 1);
    });
  }

  /**
   * Display a code block with syntax highlighting
   * @param {string} code - Code content
   * @param {string} language - Programming language
   */
  static displayCodeBlock(code, language = '') {
    const header = language 
      ? UIHelpers.colors.secondary(`Code (${language}):`)
      : UIHelpers.colors.secondary('Code:');
    
    UIHelpers.print(header, 0, 0);
    
    // Simple code formatting (could be enhanced with syntax highlighting)
    const codeLines = code.split('\n').map((line, index) => {
      const lineNumber = (index + 1).toString().padStart(3, ' ');
      return UIHelpers.colors.muted(`${lineNumber} │ `) + line;
    });

    const codeBlock = UIHelpers.createInfoBox(codeLines.join('\n'), 'info');
    UIHelpers.print(codeBlock, 0, 1);
  }

  /**
   * Display a summary of the conversation
   * @param {Array} messages - Array of message objects
   * @param {object} stats - Conversation statistics
   */
  static displaySummary(messages, stats = {}) {
    const summary = [
      UIHelpers.colors.primary.bold('Conversation Summary:'),
      '',
      `Messages: ${messages.length}`,
      `User messages: ${messages.filter(m => m.role === 'user').length}`,
      `Assistant messages: ${messages.filter(m => m.role === 'assistant').length}`
    ];

    if (stats.totalTokens) {
      summary.push(`Total tokens: ${stats.totalTokens}`);
    }

    if (stats.duration) {
      summary.push(`Duration: ${stats.duration}`);
    }

    UIHelpers.print(summary.join('\n'), 0, 1);
  }
}

export default ResponseFormatter;
