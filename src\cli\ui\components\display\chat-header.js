import UIHelpers from '../../utils/ui-helpers.js';

/**
 * Chat header component for displaying provider and model information
 */
export class ChatHeader {
  /**
   * Display the chat header with provider and model information
   * @param {string} providerName - Name of the active provider
   * @param {string} modelName - Name of the current model
   * @param {object} options - Display options
   */
  static display(providerName, modelName, options = {}) {
    const defaultOptions = {
      clearScreen: true,
      showBorder: true,
      ...options
    };

    if (defaultOptions.clearScreen) {
      UIHelpers.clearScreen();
    }

    const title = `Chat with ${this.formatProviderName(providerName)}`;
    const subtitle = `Model: ${modelName}`;

    const header = UIHelpers.createHeader(title, subtitle, {
      padding: 1,
      borderStyle: 'round',
      borderColor: 'cyan',
      textAlignment: 'center'
    });

    UIHelpers.print(header, 0, 1);
  }

  /**
   * Format provider name for display
   * @param {string} providerName - Raw provider name
   * @returns {string} Formatted provider name
   */
  static formatProviderName(providerName) {
    if (!providerName) return 'Unknown Provider';
    
    // Capitalize first letter and handle common provider names
    const formatted = providerName.charAt(0).toUpperCase() + providerName.slice(1);
    
    // Handle specific provider formatting
    const providerMap = {
      'openai': 'OpenAI',
      'anthropic': 'Anthropic',
      'google': 'Google',
      'cohere': 'Cohere',
      'huggingface': 'Hugging Face'
    };

    return providerMap[providerName.toLowerCase()] || formatted;
  }

  /**
   * Display a compact header (single line)
   * @param {string} providerName - Name of the active provider
   * @param {string} modelName - Name of the current model
   */
  static displayCompact(providerName, modelName) {
    const provider = this.formatProviderName(providerName);
    const headerText = `${UIHelpers.colors.primary(provider)} ${UIHelpers.colors.muted('|')} ${UIHelpers.colors.secondary(modelName)}`;
    
    UIHelpers.print(headerText, 0, 1);
  }

  /**
   * Display header with connection status
   * @param {string} providerName - Name of the active provider
   * @param {string} modelName - Name of the current model
   * @param {boolean} isConnected - Connection status
   */
  static displayWithStatus(providerName, modelName, isConnected = true) {
    const title = `Chat with ${this.formatProviderName(providerName)}`;
    const status = isConnected 
      ? UIHelpers.colors.success('● Connected') 
      : UIHelpers.colors.error('● Disconnected');
    const subtitle = `Model: ${modelName} | ${status}`;

    const header = UIHelpers.createHeader(title, subtitle, {
      padding: 1,
      borderStyle: 'round',
      borderColor: isConnected ? 'cyan' : 'red',
      textAlignment: 'center'
    });

    UIHelpers.print(header, 0, 1);
  }

  /**
   * Update header with new model information
   * @param {string} providerName - Name of the active provider
   * @param {string} newModelName - Name of the new model
   * @param {string} previousModelName - Name of the previous model
   */
  static displayModelChange(providerName, newModelName, previousModelName) {
    this.display(providerName, newModelName);
    
    const changeMessage = UIHelpers.colors.success(
      `Model changed from ${previousModelName} to ${newModelName}`
    );
    
    UIHelpers.print(changeMessage, 0, 1);
  }

  /**
   * Display header with additional context information
   * @param {string} providerName - Name of the active provider
   * @param {string} modelName - Name of the current model
   * @param {object} context - Additional context information
   */
  static displayWithContext(providerName, modelName, context = {}) {
    const title = `Chat with ${this.formatProviderName(providerName)}`;
    let subtitle = `Model: ${modelName}`;
    
    if (context.messageCount) {
      subtitle += ` | Messages: ${context.messageCount}`;
    }
    
    if (context.tokenCount) {
      subtitle += ` | Tokens: ${context.tokenCount}`;
    }

    const header = UIHelpers.createHeader(title, subtitle, {
      padding: 1,
      borderStyle: 'round',
      borderColor: 'cyan',
      textAlignment: 'center'
    });

    UIHelpers.print(header, 0, 1);
  }
}

export default ChatHeader;
