import inquirer from 'inquirer';
import UIHelpers from '../../utils/ui-helpers.js';
import { InputValidator } from './input-validator.js';

/**
 * Message input component for handling user input
 */
export class MessageInput {
  /**
   * Get user message input
   * @param {object} options - Input options
   * @returns {Promise<string>} User input
   */
  static async getMessage(options = {}) {
    const defaultOptions = {
      prompt: '▶',
      placeholder: 'Type your message...',
      validate: true,
      allowEmpty: false,
      maxLength: null,
      multiline: false,
      ...options
    };

    try {
      const promptConfig = this.createPromptConfig(defaultOptions);
      const { message } = await inquirer.prompt([promptConfig]);
      
      return message.trim();
    } catch (error) {
      if (error.isTtyError) {
        throw new Error('Interactive input not available in this environment');
      }
      throw error;
    }
  }

  /**
   * Create inquirer prompt configuration
   * @param {object} options - Input options
   * @returns {object} Prompt configuration
   */
  static createPromptConfig(options) {
    const config = {
      type: options.multiline ? 'editor' : 'input',
      name: 'message',
      message: UIHelpers.colors.text(options.prompt),
      prefix: '',
      suffix: '',
    };

    // Add transformer for placeholder text
    if (!options.multiline) {
      config.transformer = (input, _, flags) => {
        if (!input && !flags.isFinal) {
          return UIHelpers.colors.muted(options.placeholder);
        }
        return input;
      };
    }

    // Add validation
    if (options.validate) {
      config.validate = (input) => {
        return InputValidator.validateMessage(input, {
          allowEmpty: options.allowEmpty,
          maxLength: options.maxLength
        });
      };
    }

    return config;
  }

  /**
   * Get confirmation input
   * @param {string} message - Confirmation message
   * @param {boolean} defaultValue - Default value
   * @returns {Promise<boolean>} User confirmation
   */
  static async getConfirmation(message, defaultValue = false) {
    try {
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: UIHelpers.colors.text(message),
          default: defaultValue,
          prefix: '',
          suffix: ''
        }
      ]);

      return confirm;
    } catch (error) {
      if (error.isTtyError) {
        throw new Error('Interactive input not available in this environment');
      }
      throw error;
    }
  }

  /**
   * Get selection from a list
   * @param {string} message - Selection message
   * @param {Array} choices - Array of choices
   * @param {object} options - Selection options
   * @returns {Promise<string>} Selected choice
   */
  static async getSelection(message, choices, options = {}) {
    const defaultOptions = {
      pageSize: 10,
      loop: true,
      ...options
    };

    try {
      const { selection } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selection',
          message: UIHelpers.colors.text(message),
          choices: choices,
          pageSize: defaultOptions.pageSize,
          loop: defaultOptions.loop,
          prefix: '',
          suffix: ''
        }
      ]);

      return selection;
    } catch (error) {
      if (error.isTtyError) {
        throw new Error('Interactive input not available in this environment');
      }
      throw error;
    }
  }

  /**
   * Get multiple selections from a list
   * @param {string} message - Selection message
   * @param {Array} choices - Array of choices
   * @param {object} options - Selection options
   * @returns {Promise<Array>} Selected choices
   */
  static async getMultipleSelection(message, choices, options = {}) {
    const defaultOptions = {
      pageSize: 10,
      validate: null,
      ...options
    };

    try {
      const { selections } = await inquirer.prompt([
        {
          type: 'checkbox',
          name: 'selections',
          message: UIHelpers.colors.text(message),
          choices: choices,
          pageSize: defaultOptions.pageSize,
          validate: defaultOptions.validate,
          prefix: '',
          suffix: ''
        }
      ]);

      return selections;
    } catch (error) {
      if (error.isTtyError) {
        throw new Error('Interactive input not available in this environment');
      }
      throw error;
    }
  }

  /**
   * Get text input with custom validation
   * @param {string} message - Input message
   * @param {object} options - Input options
   * @returns {Promise<string>} User input
   */
  static async getTextInput(message, options = {}) {
    const defaultOptions = {
      placeholder: '',
      validate: null,
      filter: null,
      mask: false,
      ...options
    };

    try {
      const promptConfig = {
        type: defaultOptions.mask ? 'password' : 'input',
        name: 'input',
        message: UIHelpers.colors.text(message),
        prefix: '',
        suffix: ''
      };

      if (defaultOptions.placeholder && !defaultOptions.mask) {
        promptConfig.transformer = (input, _, flags) => {
          if (!input && !flags.isFinal) {
            return UIHelpers.colors.muted(defaultOptions.placeholder);
          }
          return input;
        };
      }

      if (defaultOptions.validate) {
        promptConfig.validate = defaultOptions.validate;
      }

      if (defaultOptions.filter) {
        promptConfig.filter = defaultOptions.filter;
      }

      const { input } = await inquirer.prompt([promptConfig]);
      return input;
    } catch (error) {
      if (error.isTtyError) {
        throw new Error('Interactive input not available in this environment');
      }
      throw error;
    }
  }

  /**
   * Get number input
   * @param {string} message - Input message
   * @param {object} options - Input options
   * @returns {Promise<number>} User input as number
   */
  static async getNumberInput(message, options = {}) {
    const defaultOptions = {
      min: null,
      max: null,
      integer: false,
      ...options
    };

    const validate = (input) => {
      const num = defaultOptions.integer ? parseInt(input) : parseFloat(input);
      
      if (isNaN(num)) {
        return 'Please enter a valid number';
      }

      if (defaultOptions.min !== null && num < defaultOptions.min) {
        return `Number must be at least ${defaultOptions.min}`;
      }

      if (defaultOptions.max !== null && num > defaultOptions.max) {
        return `Number must be at most ${defaultOptions.max}`;
      }

      return true;
    };

    const filter = (input) => {
      return defaultOptions.integer ? parseInt(input) : parseFloat(input);
    };

    return this.getTextInput(message, {
      validate,
      filter,
      ...options
    });
  }

  /**
   * Get file path input with validation
   * @param {string} message - Input message
   * @param {object} options - Input options
   * @returns {Promise<string>} File path
   */
  static async getFilePathInput(message, options = {}) {
    const defaultOptions = {
      mustExist: false,
      extension: null,
      ...options
    };

    const validate = (input) => {
      return InputValidator.validateFilePath(input, defaultOptions);
    };

    return this.getTextInput(message, {
      validate,
      placeholder: 'Enter file path...',
      ...options
    });
  }
}

export default MessageInput;
