import fs from 'fs/promises';
import path from 'path';
import chalk from 'chalk';
import logger from '../../../utils/logger.js';

/**
 * File operations utility for conversation management
 */
export class FileOperations {
  /**
   * Save conversation to a text file
   * @param {Array} messages - Array of conversation messages
   * @param {string} providerName - Name of the LLM provider
   * @param {string} modelName - Name of the model used
   * @param {string} filename - Optional filename
   * @returns {Promise<string>} Path to saved file
   */
  static async saveConversation(messages, providerName, modelName, filename = null) {
    if (!messages || messages.length === 0) {
      throw new Error('No conversation to save');
    }

    try {
      const defaultFilename = filename || `conversation-${Date.now()}.txt`;
      const filepath = path.resolve(defaultFilename);
      
      const content = this.formatConversationContent(messages, providerName, modelName);
      
      await fs.writeFile(filepath, content, 'utf8');
      logger.info(`Conversation saved to ${filepath}`);
      
      return filepath;
    } catch (error) {
      logger.error('Error saving conversation:', error.message);
      throw new Error(`Failed to save conversation: ${error.message}`);
    }
  }

  /**
   * Format conversation content for file output
   * @param {Array} messages - Array of conversation messages
   * @param {string} providerName - Name of the LLM provider
   * @param {string} modelName - Name of the model used
   * @returns {string} Formatted conversation content
   */
  static formatConversationContent(messages, providerName, modelName) {
    let content = `LLM CLI Conversation\n`;
    content += `Date: ${new Date().toISOString()}\n`;
    content += `Provider: ${providerName}\n`;
    content += `Model: ${modelName}\n`;
    content += `Messages: ${messages.length}\n`;
    content += `\n${'='.repeat(50)}\n\n`;
    
    messages.forEach((msg, index) => {
      const role = msg.role === 'user' ? 'You' : 'Assistant';
      content += `${index + 1}. ${role}:\n${msg.content}\n\n`;
    });
    
    return content;
  }

  /**
   * Load conversation from a text file
   * @param {string} filepath - Path to conversation file
   * @returns {Promise<object>} Parsed conversation data
   */
  static async loadConversation(filepath) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      return this.parseConversationContent(content);
    } catch (error) {
      logger.error('Error loading conversation:', error.message);
      throw new Error(`Failed to load conversation: ${error.message}`);
    }
  }

  /**
   * Parse conversation content from file
   * @param {string} content - File content
   * @returns {object} Parsed conversation data
   */
  static parseConversationContent(content) {
    const lines = content.split('\n');
    const metadata = {};
    const messages = [];
    
    let inMessages = false;
    let currentMessage = null;
    
    for (const line of lines) {
      if (line.startsWith('Date:')) {
        metadata.date = line.substring(5).trim();
      } else if (line.startsWith('Provider:')) {
        metadata.provider = line.substring(9).trim();
      } else if (line.startsWith('Model:')) {
        metadata.model = line.substring(6).trim();
      } else if (line.includes('='.repeat(50))) {
        inMessages = true;
        continue;
      }
      
      if (inMessages && line.trim()) {
        const messageMatch = line.match(/^(\d+)\. (You|Assistant):/);
        if (messageMatch) {
          if (currentMessage) {
            messages.push(currentMessage);
          }
          currentMessage = {
            role: messageMatch[2] === 'You' ? 'user' : 'assistant',
            content: ''
          };
        } else if (currentMessage) {
          currentMessage.content += (currentMessage.content ? '\n' : '') + line;
        }
      }
    }
    
    if (currentMessage) {
      messages.push(currentMessage);
    }
    
    return { metadata, messages };
  }

  /**
   * Check if file exists
   * @param {string} filepath - Path to check
   * @returns {Promise<boolean>} True if file exists
   */
  static async fileExists(filepath) {
    try {
      await fs.access(filepath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats
   * @param {string} filepath - Path to file
   * @returns {Promise<object>} File stats
   */
  static async getFileStats(filepath) {
    try {
      return await fs.stat(filepath);
    } catch (error) {
      throw new Error(`Failed to get file stats: ${error.message}`);
    }
  }

  /**
   * Create directory if it doesn't exist
   * @param {string} dirPath - Directory path
   * @returns {Promise<void>}
   */
  static async ensureDirectory(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create directory: ${error.message}`);
    }
  }

  /**
   * Generate a safe filename
   * @param {string} baseName - Base filename
   * @param {string} extension - File extension
   * @returns {string} Safe filename
   */
  static generateSafeFilename(baseName, extension = 'txt') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const safeName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');
    return `${safeName}-${timestamp}.${extension}`;
  }
}

export default FileOperations;
