import UIHelpers from '../../utils/ui-helpers.js';

/**
 * Instructions display component for showing available commands and help
 */
export class InstructionsDisplay {
  /**
   * Default command list
   */
  static defaultCommands = [
    { name: 'help', description: 'Show available commands' },
    { name: 'model', description: 'Change current model' },
    { name: 'clear', description: 'Clear conversation history' },
    { name: 'config', description: 'Open configuration settings' },
    { name: 'history', description: 'Show conversation history' },
    { name: 'save', description: 'Save conversation to file' },
    { name: 'exit', description: 'Exit the chat interface' },
    { name: 'quit', description: 'Exit the chat interface' }
  ];

  /**
   * Display the main instructions
   * @param {Array} commands - Optional custom command list
   * @param {object} options - Display options
   */
  static display(commands = null, options = {}) {
    const defaultOptions = {
      showTitle: true,
      showUsageHint: true,
      compact: false,
      ...options
    };

    const commandList = commands || this.defaultCommands;

    if (defaultOptions.showTitle) {
      UIHelpers.print(UIHelpers.colors.muted('Available Commands:'), 0, 0);
    }

    if (defaultOptions.compact) {
      this.displayCompact(commandList);
    } else {
      this.displayDetailed(commandList);
    }

    if (defaultOptions.showUsageHint) {
      UIHelpers.print('', 0, 0);
      UIHelpers.print(
        UIHelpers.colors.muted('Type your message and press Enter to send.'),
        0, 1
      );
    }
  }

  /**
   * Display detailed command list
   * @param {Array} commands - Command list
   */
  static displayDetailed(commands) {
    const formattedHelp = UIHelpers.formatCommandHelp(commands);
    UIHelpers.print(formattedHelp, 0, 0);
  }

  /**
   * Display compact command list
   * @param {Array} commands - Command list
   */
  static displayCompact(commands) {
    const commandNames = commands.map(cmd => `/${cmd.name}`).join(', ');
    const compactText = UIHelpers.colors.muted(`Commands: ${commandNames}`);
    UIHelpers.print(compactText, 0, 0);
  }

  /**
   * Display help for a specific command
   * @param {string} commandName - Name of the command
   * @param {Array} commands - Command list to search
   */
  static displayCommandHelp(commandName, commands = null) {
    const commandList = commands || this.defaultCommands;
    const command = commandList.find(cmd => cmd.name === commandName);

    if (!command) {
      UIHelpers.print(
        UIHelpers.colors.error(`Unknown command: /${commandName}`),
        0, 1
      );
      return;
    }

    const helpText = `/${command.name} - ${command.description}`;
    UIHelpers.print(UIHelpers.colors.secondary(helpText), 0, 1);

    // Show additional help if available
    if (command.usage) {
      UIHelpers.print(
        UIHelpers.colors.muted(`Usage: ${command.usage}`),
        0, 1
      );
    }

    if (command.examples) {
      UIHelpers.print(UIHelpers.colors.muted('Examples:'), 0, 0);
      command.examples.forEach(example => {
        UIHelpers.print(UIHelpers.colors.muted(`  ${example}`), 0, 0);
      });
      UIHelpers.print('', 0, 1);
    }
  }

  /**
   * Display welcome message with instructions
   * @param {string} providerName - Name of the active provider
   * @param {string} modelName - Name of the current model
   */
  static displayWelcome(providerName, modelName) {
    const welcomeText = [
      `Welcome to the LLM CLI Chat Interface`,
      `Connected to ${UIHelpers.colors.primary(providerName)} using ${UIHelpers.colors.secondary(modelName)}`,
      '',
      'Start typing your message or use one of the commands below:'
    ].join('\n');

    UIHelpers.print(welcomeText, 0, 1);
    this.display();
  }

  /**
   * Display quick start guide
   */
  static displayQuickStart() {
    const quickStartText = [
      UIHelpers.colors.primary.bold('Quick Start Guide:'),
      '',
      UIHelpers.colors.secondary('1. Type your message and press Enter'),
      UIHelpers.colors.secondary('2. Use /model to change the AI model'),
      UIHelpers.colors.secondary('3. Use /clear to start a new conversation'),
      UIHelpers.colors.secondary('4. Use /help to see all commands'),
      UIHelpers.colors.secondary('5. Use /exit to quit the chat'),
      '',
      UIHelpers.colors.muted('Tip: Commands start with / (forward slash)')
    ].join('\n');

    UIHelpers.print(quickStartText, 0, 1);
  }

  /**
   * Display keyboard shortcuts
   */
  static displayShortcuts() {
    const shortcuts = [
      { key: 'Ctrl+C', description: 'Exit chat interface' },
      { key: 'Ctrl+L', description: 'Clear screen (same as /clear)' },
      { key: 'Tab', description: 'Auto-complete commands' },
      { key: 'Up/Down', description: 'Navigate command history' }
    ];

    UIHelpers.print(UIHelpers.colors.muted('Keyboard Shortcuts:'), 0, 0);
    
    shortcuts.forEach(shortcut => {
      const line = `  ${UIHelpers.colors.secondary(shortcut.key.padEnd(12))} - ${shortcut.description}`;
      UIHelpers.print(UIHelpers.colors.muted(line), 0, 0);
    });

    UIHelpers.print('', 0, 1);
  }

  /**
   * Display error message with help suggestion
   * @param {string} error - Error message
   * @param {string} suggestion - Optional suggestion
   */
  static displayError(error, suggestion = null) {
    UIHelpers.print(UIHelpers.colors.error(`Error: ${error}`), 0, 0);
    
    if (suggestion) {
      UIHelpers.print(UIHelpers.colors.muted(`Suggestion: ${suggestion}`), 0, 0);
    }
    
    UIHelpers.print(UIHelpers.colors.muted('Type /help for available commands'), 0, 1);
  }

  /**
   * Display status message
   * @param {string} message - Status message
   * @param {string} type - Message type (info, success, warning, error)
   */
  static displayStatus(message, type = 'info') {
    const colorMap = {
      info: UIHelpers.colors.text,
      success: UIHelpers.colors.success,
      warning: UIHelpers.colors.warning,
      error: UIHelpers.colors.error
    };

    const colorFn = colorMap[type] || UIHelpers.colors.text;
    UIHelpers.print(colorFn(message), 0, 1);
  }
}

export default InstructionsDisplay;
