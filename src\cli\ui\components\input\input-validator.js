import fs from 'fs/promises';
import path from 'path';

/**
 * Input validation utilities
 */
export class InputValidator {
  /**
   * Validate message input
   * @param {string} input - User input
   * @param {object} options - Validation options
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateMessage(input, options = {}) {
    const defaultOptions = {
      allowEmpty: false,
      maxLength: 10000,
      minLength: 1,
      ...options
    };

    const trimmed = input.trim();

    // Check if empty
    if (!trimmed && !defaultOptions.allowEmpty) {
      return 'Please enter a message';
    }

    // Check minimum length
    if (trimmed.length < defaultOptions.minLength) {
      return `Message must be at least ${defaultOptions.minLength} characters`;
    }

    // Check maximum length
    if (defaultOptions.maxLength && trimmed.length > defaultOptions.maxLength) {
      return `Message must be no more than ${defaultOptions.maxLength} characters`;
    }

    return true;
  }

  /**
   * Validate command input
   * @param {string} input - Command input
   * @param {Array} validCommands - Array of valid command names
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateCommand(input, validCommands = []) {
    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter a command';
    }

    if (!trimmed.startsWith('/')) {
      return 'Commands must start with /';
    }

    const commandName = trimmed.slice(1).split(' ')[0].toLowerCase();

    if (validCommands.length > 0 && !validCommands.includes(commandName)) {
      return `Unknown command: /${commandName}. Type /help for available commands`;
    }

    return true;
  }

  /**
   * Validate file path input
   * @param {string} input - File path input
   * @param {object} options - Validation options
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateFilePath(input, options = {}) {
    const defaultOptions = {
      mustExist: false,
      mustNotExist: false,
      extension: null,
      allowRelative: true,
      ...options
    };

    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter a file path';
    }

    // Check for invalid characters (basic check)
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(trimmed)) {
      return 'File path contains invalid characters';
    }

    // Check extension if specified
    if (defaultOptions.extension) {
      const ext = path.extname(trimmed).toLowerCase();
      const requiredExt = defaultOptions.extension.toLowerCase();
      if (ext !== requiredExt) {
        return `File must have ${requiredExt} extension`;
      }
    }

    // Check if relative paths are allowed
    if (!defaultOptions.allowRelative && !path.isAbsolute(trimmed)) {
      return 'Absolute path required';
    }

    return true;
  }

  /**
   * Validate file path existence (async)
   * @param {string} filePath - File path to check
   * @param {object} options - Validation options
   * @returns {Promise<boolean|string>} True if valid, error message if invalid
   */
  static async validateFilePathExists(filePath, options = {}) {
    const defaultOptions = {
      mustExist: true,
      mustNotExist: false,
      ...options
    };

    try {
      const exists = await this.fileExists(filePath);

      if (defaultOptions.mustExist && !exists) {
        return 'File does not exist';
      }

      if (defaultOptions.mustNotExist && exists) {
        return 'File already exists';
      }

      return true;
    } catch (error) {
      return `Error checking file: ${error.message}`;
    }
  }

  /**
   * Validate email format
   * @param {string} input - Email input
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateEmail(input) {
    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter an email address';
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmed)) {
      return 'Please enter a valid email address';
    }

    return true;
  }

  /**
   * Validate URL format
   * @param {string} input - URL input
   * @param {object} options - Validation options
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateUrl(input, options = {}) {
    const defaultOptions = {
      requireProtocol: true,
      allowedProtocols: ['http', 'https'],
      ...options
    };

    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter a URL';
    }

    try {
      const url = new URL(trimmed);

      if (defaultOptions.requireProtocol && !url.protocol) {
        return 'URL must include protocol (http:// or https://)';
      }

      if (defaultOptions.allowedProtocols.length > 0) {
        const protocol = url.protocol.slice(0, -1); // Remove trailing colon
        if (!defaultOptions.allowedProtocols.includes(protocol)) {
          return `Protocol must be one of: ${defaultOptions.allowedProtocols.join(', ')}`;
        }
      }

      return true;
    } catch (error) {
      return 'Please enter a valid URL';
    }
  }

  /**
   * Validate API key format
   * @param {string} input - API key input
   * @param {object} options - Validation options
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateApiKey(input, options = {}) {
    const defaultOptions = {
      minLength: 10,
      maxLength: 200,
      allowedChars: /^[a-zA-Z0-9_-]+$/,
      ...options
    };

    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter an API key';
    }

    if (trimmed.length < defaultOptions.minLength) {
      return `API key must be at least ${defaultOptions.minLength} characters`;
    }

    if (trimmed.length > defaultOptions.maxLength) {
      return `API key must be no more than ${defaultOptions.maxLength} characters`;
    }

    if (defaultOptions.allowedChars && !defaultOptions.allowedChars.test(trimmed)) {
      return 'API key contains invalid characters';
    }

    return true;
  }

  /**
   * Validate number input
   * @param {string} input - Number input
   * @param {object} options - Validation options
   * @returns {boolean|string} True if valid, error message if invalid
   */
  static validateNumber(input, options = {}) {
    const defaultOptions = {
      min: null,
      max: null,
      integer: false,
      positive: false,
      ...options
    };

    const trimmed = input.trim();

    if (!trimmed) {
      return 'Please enter a number';
    }

    const num = defaultOptions.integer ? parseInt(trimmed) : parseFloat(trimmed);

    if (isNaN(num)) {
      return 'Please enter a valid number';
    }

    if (defaultOptions.positive && num <= 0) {
      return 'Number must be positive';
    }

    if (defaultOptions.min !== null && num < defaultOptions.min) {
      return `Number must be at least ${defaultOptions.min}`;
    }

    if (defaultOptions.max !== null && num > defaultOptions.max) {
      return `Number must be at most ${defaultOptions.max}`;
    }

    return true;
  }

  /**
   * Check if file exists
   * @param {string} filePath - File path to check
   * @returns {Promise<boolean>} True if file exists
   */
  static async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate multiple inputs
   * @param {Array} validations - Array of validation objects
   * @returns {boolean|string} True if all valid, first error message if any invalid
   */
  static validateMultiple(validations) {
    for (const validation of validations) {
      const result = validation.validator(validation.input, validation.options);
      if (result !== true) {
        return result;
      }
    }
    return true;
  }
}

export default InputValidator;
