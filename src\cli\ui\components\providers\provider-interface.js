import { EventEmitter } from 'events';
import logger from '../../../../utils/logger.js';

/**
 * Provider interface wrapper for LLM provider communication
 */
export class ProviderInterface extends EventEmitter {
  constructor(provider) {
    super();
    this.provider = provider;
    this.isConnected = false;
    this.lastError = null;
    this.requestCount = 0;
    this.totalTokens = 0;
    this.startTime = new Date();
  }

  /**
   * Send a message to the LLM provider
   * @param {Array} messages - Array of message objects
   * @param {object} options - Request options
   * @returns {Promise<object>} Response object
   */
  async sendMessage(messages, options = {}) {
    try {
      this.emit('requestStarted', { messages, options });
      this.requestCount++;

      // Validate inputs
      this.validateMessages(messages);
      this.validateOptions(options);

      // Send request to provider
      const startTime = Date.now();
      const response = await this.provider.sendMessage(messages, options);
      const duration = Date.now() - startTime;

      // Process response
      const processedResponse = this.processResponse(response, duration);

      // Update statistics
      if (processedResponse.usage && processedResponse.usage.totalTokens) {
        this.totalTokens += processedResponse.usage.totalTokens;
      }

      this.isConnected = true;
      this.lastError = null;

      this.emit('requestCompleted', {
        response: processedResponse,
        duration,
        tokenCount: processedResponse.usage?.totalTokens || 0
      });

      logger.debug('Message sent successfully', {
        provider: this.provider.name,
        model: options.model,
        messageCount: messages.length,
        duration,
        tokens: processedResponse.usage?.totalTokens
      });

      return processedResponse;

    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Get available models from the provider
   * @returns {Promise<Array>} Array of available models
   */
  async getAvailableModels() {
    try {
      if (!this.provider.getAvailableModels) {
        throw new Error('Provider does not support model listing');
      }

      const models = await this.provider.getAvailableModels();
      
      this.emit('modelsRetrieved', { models });
      
      logger.debug('Models retrieved', {
        provider: this.provider.name,
        modelCount: models.length
      });

      return models;

    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Test connection to the provider
   * @returns {Promise<boolean>} True if connection is successful
   */
  async testConnection() {
    try {
      // Send a simple test message
      const testMessages = [
        { role: 'user', content: 'Hello' }
      ];

      const response = await this.sendMessage(testMessages, {
        model: this.getDefaultModel(),
        maxTokens: 10
      });

      this.isConnected = true;
      this.emit('connectionTested', { success: true });
      
      logger.info('Connection test successful', {
        provider: this.provider.name
      });

      return true;

    } catch (error) {
      this.isConnected = false;
      this.emit('connectionTested', { success: false, error });
      
      logger.warn('Connection test failed', {
        provider: this.provider.name,
        error: error.message
      });

      return false;
    }
  }

  /**
   * Get provider information
   * @returns {object} Provider information
   */
  getProviderInfo() {
    return {
      name: this.provider.name || 'Unknown',
      type: this.provider.type || 'Unknown',
      version: this.provider.version || 'Unknown',
      isConnected: this.isConnected,
      lastError: this.lastError,
      statistics: this.getStatistics()
    };
  }

  /**
   * Get usage statistics
   * @returns {object} Usage statistics
   */
  getStatistics() {
    const uptime = new Date() - this.startTime;
    
    return {
      requestCount: this.requestCount,
      totalTokens: this.totalTokens,
      uptime: this.formatDuration(uptime),
      uptimeMs: uptime,
      averageTokensPerRequest: this.requestCount > 0 
        ? Math.round(this.totalTokens / this.requestCount) 
        : 0,
      isConnected: this.isConnected,
      lastError: this.lastError
    };
  }

  /**
   * Reset statistics
   */
  resetStatistics() {
    this.requestCount = 0;
    this.totalTokens = 0;
    this.startTime = new Date();
    this.lastError = null;

    this.emit('statisticsReset');
    logger.debug('Statistics reset', { provider: this.provider.name });
  }

  /**
   * Validate message array
   * @param {Array} messages - Messages to validate
   */
  validateMessages(messages) {
    if (!Array.isArray(messages)) {
      throw new Error('Messages must be an array');
    }

    if (messages.length === 0) {
      throw new Error('Messages array cannot be empty');
    }

    messages.forEach((message, index) => {
      if (!message || typeof message !== 'object') {
        throw new Error(`Message at index ${index} must be an object`);
      }

      if (!message.role || typeof message.role !== 'string') {
        throw new Error(`Message at index ${index} must have a valid role`);
      }

      if (!message.content || typeof message.content !== 'string') {
        throw new Error(`Message at index ${index} must have valid content`);
      }

      const validRoles = ['user', 'assistant', 'system'];
      if (!validRoles.includes(message.role)) {
        throw new Error(`Message at index ${index} has invalid role: ${message.role}`);
      }
    });
  }

  /**
   * Validate request options
   * @param {object} options - Options to validate
   */
  validateOptions(options) {
    if (options && typeof options !== 'object') {
      throw new Error('Options must be an object');
    }

    if (options.model && typeof options.model !== 'string') {
      throw new Error('Model must be a string');
    }

    if (options.maxTokens && (typeof options.maxTokens !== 'number' || options.maxTokens <= 0)) {
      throw new Error('Max tokens must be a positive number');
    }

    if (options.temperature && (typeof options.temperature !== 'number' || options.temperature < 0 || options.temperature > 2)) {
      throw new Error('Temperature must be a number between 0 and 2');
    }
  }

  /**
   * Process provider response
   * @param {object} response - Raw provider response
   * @param {number} duration - Request duration in ms
   * @returns {object} Processed response
   */
  processResponse(response, duration) {
    const processed = {
      content: response.content || '',
      model: response.model || 'unknown',
      usage: response.usage || null,
      metadata: {
        duration,
        timestamp: new Date(),
        provider: this.provider.name,
        requestId: this.generateRequestId()
      }
    };

    // Ensure usage object has expected structure
    if (processed.usage) {
      processed.usage = {
        promptTokens: processed.usage.promptTokens || 0,
        completionTokens: processed.usage.completionTokens || 0,
        totalTokens: processed.usage.totalTokens || 
          (processed.usage.promptTokens || 0) + (processed.usage.completionTokens || 0)
      };
    }

    return processed;
  }

  /**
   * Handle errors
   * @param {Error} error - Error to handle
   */
  handleError(error) {
    this.lastError = {
      message: error.message,
      timestamp: new Date(),
      type: error.constructor.name
    };

    this.isConnected = false;
    this.emit('error', error);

    logger.error('Provider interface error', {
      provider: this.provider.name,
      error: error.message,
      type: error.constructor.name
    });
  }

  /**
   * Get default model for the provider
   * @returns {string} Default model name
   */
  getDefaultModel() {
    if (this.provider.getDefaultModel) {
      return this.provider.getDefaultModel();
    }
    
    // Fallback to common default models
    const defaultModels = {
      openai: 'gpt-3.5-turbo',
      anthropic: 'claude-3-sonnet-20240229',
      google: 'gemini-pro',
      cohere: 'command'
    };

    return defaultModels[this.provider.name] || 'default';
  }

  /**
   * Generate a unique request ID
   * @returns {string} Request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format duration in human-readable format
   * @param {number} ms - Duration in milliseconds
   * @returns {string} Formatted duration
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.removeAllListeners();
    this.isConnected = false;
    
    if (this.provider && this.provider.cleanup) {
      this.provider.cleanup();
    }

    logger.debug('Provider interface cleaned up', {
      provider: this.provider.name
    });
  }
}

export default ProviderInterface;
