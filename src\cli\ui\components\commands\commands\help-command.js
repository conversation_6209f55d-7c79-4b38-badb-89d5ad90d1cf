import UIHelpers from '../../../utils/ui-helpers.js';
import { InstructionsDisplay } from '../../display/instructions-display.js';

/**
 * Help command implementation
 */
export const HelpCommand = {
  name: 'help',
  description: 'Show available commands and usage information',
  usage: '/help [command]',
  examples: [
    '/help',
    '/help model',
    '/help clear'
  ],
  category: 'general',
  aliases: ['h', '?'],

  /**
   * Execute help command
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   */
  async execute(args, context) {
    const commandName = args.positional[0];

    if (commandName) {
      // Show help for specific command
      return this.showCommandHelp(commandName, context);
    } else {
      // Show general help
      return this.showGeneralHelp(context);
    }
  },

  /**
   * Show help for a specific command
   * @param {string} commandName - Command name
   * @param {object} context - Execution context
   */
  async showCommandHelp(commandName, context) {
    const commandHandler = context.commandHandler;
    const helpResult = commandHandler.getHelp(commandName);

    if (!helpResult.success) {
      UIHelpers.print(UIHelpers.colors.error(helpResult.message), 0, 1);
      return;
    }

    const command = helpResult.command;
    
    // Display command header
    const header = UIHelpers.createHeader(
      `Help: /${command.name}`,
      command.description,
      { borderColor: 'blue' }
    );
    
    UIHelpers.print(header, 0, 1);

    // Display usage
    if (command.usage) {
      UIHelpers.print(UIHelpers.colors.primary('Usage:'), 0, 0);
      UIHelpers.print(`  ${command.usage}`, 0, 1);
    }

    // Display aliases
    if (command.aliases && command.aliases.length > 0) {
      UIHelpers.print(UIHelpers.colors.primary('Aliases:'), 0, 0);
      const aliases = command.aliases.map(alias => `/${alias}`).join(', ');
      UIHelpers.print(`  ${aliases}`, 0, 1);
    }

    // Display examples
    if (command.examples && command.examples.length > 0) {
      UIHelpers.print(UIHelpers.colors.primary('Examples:'), 0, 0);
      command.examples.forEach(example => {
        UIHelpers.print(`  ${UIHelpers.colors.muted(example)}`, 0, 0);
      });
      UIHelpers.print('', 0, 1);
    }

    // Display additional information
    if (command.category) {
      UIHelpers.print(
        UIHelpers.colors.muted(`Category: ${command.category}`),
        0, 1
      );
    }
  },

  /**
   * Show general help with all commands
   * @param {object} context - Execution context
   */
  async showGeneralHelp(context) {
    const commandHandler = context.commandHandler;
    const helpResult = commandHandler.getHelp();

    if (!helpResult.success) {
      UIHelpers.print(UIHelpers.colors.error('Failed to get help information'), 0, 1);
      return;
    }

    // Display header
    const header = UIHelpers.createHeader(
      'LLM CLI Help',
      'Available commands and usage information',
      { borderColor: 'blue' }
    );
    
    UIHelpers.print(header, 0, 1);

    // Display commands by category
    const categories = helpResult.categories;
    
    Object.entries(categories).forEach(([categoryName, commands]) => {
      // Category header
      const categoryTitle = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);
      UIHelpers.print(UIHelpers.colors.primary.bold(categoryTitle + ':'), 0, 0);

      // Commands in category
      const commandList = commands.map(cmd => ({
        name: cmd.name,
        description: cmd.description
      }));

      const formattedCommands = UIHelpers.formatCommandHelp(commandList);
      UIHelpers.print(formattedCommands, 0, 1);
    });

    // Display usage tips
    this.showUsageTips();
  },

  /**
   * Show usage tips
   */
  showUsageTips() {
    const tips = [
      'Type your message and press Enter to chat with the AI',
      'Commands start with / (forward slash)',
      'Use /help <command> for detailed information about a specific command',
      'Use Tab for command auto-completion',
      'Use Ctrl+C to exit the chat interface'
    ];

    UIHelpers.print(UIHelpers.colors.primary.bold('Tips:'), 0, 0);
    tips.forEach((tip, index) => {
      UIHelpers.print(`  ${index + 1}. ${UIHelpers.colors.muted(tip)}`, 0, 0);
    });
    UIHelpers.print('', 0, 1);
  },

  /**
   * Validate help command arguments
   * @param {object} args - Command arguments
   * @param {object} context - Execution context
   * @returns {boolean|string} True if valid, error message if invalid
   */
  validate(args, context) {
    // Help command accepts optional command name
    if (args.positional.length > 1) {
      return 'Help command accepts only one argument (command name)';
    }

    return true;
  }
};

export default HelpCommand;
